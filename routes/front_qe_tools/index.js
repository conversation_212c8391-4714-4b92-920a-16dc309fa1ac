// 大前端质效工具
import caseRoutes from './case';
import planRoutes from './plan';
import workshopRoutes from './workshop';
import deviceRoutes from './device';
import intelligentRoutes from './intelligent';
import iterationCaseRoutes from './other/iterationCase';
import proxyManageRoutes from './other/proxyManage';

export const frontQeToolsRoutes = [
    {
        path: '/',
        component: '@/pages/front_qe_tools/index', // 🟢 新增入口页组件
        routes: [
            // 3.0
            caseRoutes,
            planRoutes,
            workshopRoutes,
            deviceRoutes,
            intelligentRoutes,
            proxyManageRoutes,
            // 1.0
            iterationCaseRoutes
        ]
    }
];
