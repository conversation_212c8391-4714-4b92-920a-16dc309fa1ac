import { useState, useEffect } from 'react';
import { message, Input, Select, Pagination, Spin, Card, Button, Dropdown, Modal } from 'antd';
import {
    PlusOutlined,
    SearchOutlined,
    EditOutlined,
    DeleteOutlined,
    MoreOutlined
} from '@ant-design/icons';
import PageHeader from 'COMMON/components/PageHeader';
import McpServerForm from './components/McpServerForm';
import EllipsisTooltip from 'COMMON/components/EllipsisTooltip';
import {
    getMcpServer,
    createMcpServer,
    updateMcpServer,
    deleteMcpServer
} from 'COMMON/api/qe_rag/mcpServers';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import { getJoinedList, getAllGroupsList } from 'COMMON/api/qe_rag/workgroup';
import { getAvatarColor } from 'FEATURES/qe_rag/utils';
import usePageSize from 'HOOKS/usePageSize';
import commonStyles from 'FEATURES/qe_rag/common.module.less';
import styles from './McpPage.module.less';

const McpPage = ({ joinedWorkGroupList, allWorkGroupList }) => {
    const pageSize = usePageSize({
        cardHeight: 280, // MCP卡片更高
        minItems: 12, // 最少12条
        pageName: 'MCP页面'
    });
    const [activeTab, setActiveTab] = useState('mine');
    const [loading, setLoading] = useState(false);
    const [dialogVisible, setDialogVisible] = useState(false);
    const [dialogTitle, setDialogTitle] = useState('');
    const [currentServer, setCurrentServer] = useState({
        id: '',
        name: '',
        type: '',
        command: '',
        url: '',
        env: '',
        queryPath: '',
        executePath: '',
        groupId: '',
        description: ''
    });

    const [searchParams, setSearchParams] = useState({
        name: '',
        groupId: '',
        page: 1,
        size: pageSize
    });

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: pageSize,
        total: 0
    });

    const [servers, setServers] = useState([]);

    // 获取 McpServer
    const fetchServers = async (params = searchParams, tab = activeTab) => {
        setLoading(true);
        try {
            const queryParams = {
                ...params,
                type: tab === 'all' ? 0 : 1, // 0: 全部, 1: 我的
                page: params.page.toString(),
                size: params.size.toString()
            };
            const response = await getMcpServer(queryParams);
            setServers(response.items || []);
            setPagination({
                current: response.page || 1,
                pageSize: pageSize,
                total: response.total || 0
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // 更新搜索参数中的页面大小
        const updatedParams = { ...searchParams, size: pageSize };
        setSearchParams(updatedParams);
        setPagination((prev) => ({ ...prev, pageSize }));
        fetchServers(updatedParams);
    }, [pageSize]);

    // 处理标签页切换
    const handleTabChange = (tab) => {
        setActiveTab(tab);
        const newParams = {
            ...searchParams,
            page: 1,
            size: pageSize
        };
        setSearchParams(newParams);
        fetchServers(newParams, tab);
    };

    // 处理分页变化
    const handlePageChange = (page) => {
        const newParams = {
            ...searchParams,
            page,
            size: pageSize
        };
        setSearchParams(newParams);
        fetchServers(newParams);
    };

    // 处理搜索
    const handleSearch = (values) => {
        const newParams = {
            ...searchParams,
            ...values,
            page: 1,
            size: pageSize
        };
        setSearchParams(newParams);
        fetchServers(newParams);
    };

    // 处理新建
    const handleCreate = () => {
        setDialogTitle('新建服务器');
        setCurrentServer({
            id: '',
            name: '',
            type: '',
            command: '',
            url: '',
            env: '',
            queryPath: '',
            executePath: '',
            groupId: '',
            description: ''
        });
        setDialogVisible(true);
    };

    // 处理编辑
    const handleEdit = (server) => {
        setDialogTitle('编辑服务器');
        setCurrentServer({ ...server });
        setDialogVisible(true);
    };

    // 处理保存
    const handleSave = async (server) => {
        const apiMethod = server.id ? updateMcpServer : createMcpServer;
        await apiMethod(server);
        fetchServers();
        setDialogVisible(false);
        message.success('操作成功');
    };

    // 处理删除
    const handleDelete = async (server) => {
        await deleteMcpServer({
            id: server.id,
            groupId: server.groupId
        });
        fetchServers();
        message.success('删除成功');
    };

    // 检查是否有编辑权限
    const hasEditPermission = (groupId) => {
        if (!groupId) {
            return;
        }
        return joinedWorkGroupList.some((group) => +group.id === +groupId);
    };

    // 格式化工作组信息
    const formatGroupInfo = (groupId) => {
        if (!groupId) {
            return '默认工作组';
        }

        const numericGroupId = Number(groupId);
        const group = allWorkGroupList.find((g) => g.id === numericGroupId);
        return group ? group.name : `工作组 ${groupId}`;
    };

    return (
        <div className={styles.container}>
            {/* 固定头部区域 */}
            <div className={styles.fixedHeader}>
                {/* 页面头部 */}
                <PageHeader
                    title="MCP Server"
                    tabs={[
                        { key: 'mine', label: '我的' },
                        { key: 'all', label: '全部' }
                    ]}
                    activeTab={activeTab}
                    onTabChange={handleTabChange}
                    actionButton={{
                        text: '新建服务器',
                        icon: <PlusOutlined />,
                        onClick: handleCreate
                    }}
                />

                {/* 搜索栏 */}
                <div className={styles.searchBar}>
                    <Input
                        placeholder="搜索服务器名称..."
                        prefix={<SearchOutlined />}
                        value={searchParams.name}
                        onChange={(e) => setSearchParams({ ...searchParams, name: e.target.value })}
                        onPressEnter={() => handleSearch(searchParams)}
                        className={styles.searchInput}
                    />
                    <Select
                        placeholder={'请选择工作组'}
                        value={searchParams.groupId || undefined}
                        onChange={(value) => {
                            const newParams = {
                                ...searchParams,
                                groupId: value || '',
                                page: 1,
                                size: pageSize
                            };
                            setSearchParams(newParams);
                            handleSearch(newParams);
                        }}
                        allowClear
                        className={styles.workGroupSelect}
                        style={{ width: 240 }}
                    >
                        {(activeTab === 'mine' ? joinedWorkGroupList : allWorkGroupList)?.map(
                            (group) => (
                                <Select.Option key={group.id} value={group.id}>
                                    <span className={commonStyles.groupId}>#{group.id}</span>
                                    {group.name}
                                </Select.Option>
                            )
                        )}
                    </Select>
                </div>
            </div>

            {/* 可滚动内容区域 */}
            <div className={styles.scrollableContent}>
                {/* 服务器卡片列表 */}
                <Spin spinning={loading}>
                    <div className={styles.cardGrid}>
                        {servers.map((server) => {
                            const groupName = formatGroupInfo(server.groupId);

                            // 创建下拉菜单项
                            const menuItems = [
                                {
                                    key: 'edit',
                                    label: '编辑',
                                    icon: <EditOutlined />,
                                    disabled: !hasEditPermission(server.groupId),
                                    onClick: () => handleEdit(server)
                                },
                                {
                                    key: 'delete',
                                    label: '删除',
                                    icon: <DeleteOutlined />,
                                    disabled: !hasEditPermission(server.groupId),
                                    danger: true,
                                    onClick: () => {
                                        Modal.confirm({
                                            title: '确认删除',
                                            content: '确认要删除该服务器吗？删除后无法恢复。',
                                            okText: '确认',
                                            cancelText: '取消',
                                            okType: 'danger',
                                            onOk: () => handleDelete(server)
                                        });
                                    }
                                }
                            ];

                            return (
                                <Card key={server.id} className={styles.serverCard} hoverable>
                                    <div className={styles.cardContent}>
                                        {/* 卡片头部 */}
                                        <div className={styles.cardHeader}>
                                            <div
                                                className={styles.cardIcon}
                                                style={{ background: getAvatarColor(server.name) }}
                                            >
                                                <span className={styles.iconText}>
                                                    {server.name?.charAt(0)?.toUpperCase() || 'S'}
                                                </span>
                                            </div>
                                            <div className={styles.cardTitle}>
                                                <div className={styles.titleRow}>
                                                    <h3 className={styles.serverName}>
                                                        {server.name}
                                                    </h3>
                                                    <div
                                                        className={
                                                            server.type === 0
                                                                ? styles.serverTypeApi
                                                                : styles.serverTypeLocal
                                                        }
                                                    >
                                                        {server.type === 0 ? 'API' : 'Local'}
                                                    </div>
                                                    <div className={styles.cardId}>
                                                        ID: {server.id}
                                                    </div>
                                                </div>
                                                {/* 服务器描述 */}
                                                <EllipsisTooltip
                                                    text={server.description || '暂无'}
                                                    className={styles.cardDescription}
                                                />
                                            </div>
                                            <div onClick={(e) => e.stopPropagation()}>
                                                <Dropdown
                                                    menu={{ items: menuItems }}
                                                    trigger={['hover']}
                                                    placement="bottomRight"
                                                >
                                                    <Button
                                                        type="text"
                                                        icon={<MoreOutlined />}
                                                        className={styles.moreButton}
                                                    />
                                                </Dropdown>
                                            </div>
                                        </div>

                                        {/* 服务器详细信息 */}
                                        <div className={styles.serverDetails}>
                                            <div className={styles.detailRow}>
                                                <span className={styles.detailLabel}>
                                                    执行命令:
                                                </span>
                                                <EllipsisTooltip
                                                    text={server.command || '暂无'}
                                                    className={styles.detailValue}
                                                />
                                            </div>
                                            <div className={styles.detailRow}>
                                                <span className={styles.detailLabel}>
                                                    接口地址:
                                                </span>
                                                <EllipsisTooltip
                                                    text={server.url || '暂无'}
                                                    className={styles.detailValue}
                                                />
                                            </div>
                                            <div className={styles.detailRow}>
                                                <span className={styles.detailLabel}>
                                                    配置信息:
                                                </span>
                                                <EllipsisTooltip
                                                    text={server.env || '暂无'}
                                                    className={styles.detailValue}
                                                />
                                            </div>
                                            <div className={styles.detailRow}>
                                                <span className={styles.detailLabel}>
                                                    查询路径:
                                                </span>
                                                <EllipsisTooltip
                                                    text={server.queryPath || '暂无'}
                                                    className={styles.detailValue}
                                                />
                                            </div>
                                            <div className={styles.detailRow}>
                                                <span className={styles.detailLabel}>
                                                    执行路径:
                                                </span>
                                                <EllipsisTooltip
                                                    text={server.executePath || '暂无'}
                                                    className={styles.detailValue}
                                                />
                                            </div>
                                        </div>

                                        {/* 底部信息 */}
                                        <div className={styles.cardFooter}>
                                            <div className={styles.tags}>
                                                <span className={styles.tag}>
                                                    <span className={styles.groupId}>
                                                        # {server.groupId}
                                                    </span>
                                                    {groupName}
                                                </span>
                                            </div>
                                            <div className={styles.updateTime}>
                                                {server.updateAt || server.createAt}
                                            </div>
                                        </div>
                                    </div>
                                </Card>
                            );
                        })}
                    </div>
                </Spin>

                {/* 分页 */}
                {pagination.total > pagination.pageSize && (
                    <div className={styles.pagination}>
                        <Pagination
                            current={pagination.current}
                            pageSize={pagination.pageSize}
                            total={pagination.total}
                            showSizeChanger={false}
                            simple
                            onChange={handlePageChange}
                        />
                    </div>
                )}
            </div>

            {/* 新建/编辑服务器弹窗 */}
            <McpServerForm
                visible={dialogVisible}
                title={dialogTitle}
                server={currentServer}
                onCancel={() => setDialogVisible(false)}
                onSave={handleSave}
            />
        </div>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(McpPage);
