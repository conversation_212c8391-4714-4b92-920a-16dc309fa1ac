import { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { getJoinedList } from 'COMMON/api/qe_rag/workgroup';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import styles from './McpServerForm.module.less';

const { Option } = Select;
const { TextArea } = Input;

const McpServerForm = ({ visible, title, server, onCancel, onSave, joinedWorkGroupList }) => {
    const [form] = Form.useForm();
    // 监听server变化更新表单数据
    useEffect(() => {
        if (visible && server) {
            form.setFieldsValue({
                ...server,
                type: Number(server.type),
                groupId: Number(server.groupId)
            });
        }
    }, [visible, server, form]);

    const rules = {
        name: [
            { required: true, message: '请输入服务器名称', trigger: 'blur' },
            { max: 50, message: '服务器名称不超过50个字符', trigger: 'blur' }
        ],
        type: [{ required: true, message: '请选择服务器类型', trigger: 'change' }],
        env: [{ required: true, message: '请输入配置信息', trigger: 'blur' }],
        queryPath: [{ required: true, message: '请输入查询路径', trigger: 'blur' }],
        executePath: [{ required: true, message: '请输入执行路径', trigger: 'blur' }],
        groupId: [{ required: true, message: '请选择工作组', trigger: 'change' }]
    };

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const submitData = {
                ...values,
                id: server.id, // 保留原有ID用于编辑
                type: Number(values.type),
                groupId: Number(values.groupId)
            };
            onSave(submitData);
        } catch (error) {
            message.error('请检查表单填写是否正确');
        }
    };

    const handleClose = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal
            title={'新增'}
            open={visible}
            onOk={handleSubmit}
            onCancel={handleClose}
            width={600}
            okText="保存"
            cancelText="取消"
            destroyOnClose
        >
            <Form form={form} layout="vertical" className={styles.form}>
                <Form.Item label="server名称" name="name" rules={rules.name}>
                    <Input placeholder="请输入server名称" />
                </Form.Item>

                <Form.Item label="服务器类型" name="type" rules={rules.type}>
                    <Select placeholder="请选择服务器类型">
                        <Option value={0}>api</Option>
                        <Option value={1}>local</Option>
                    </Select>
                </Form.Item>

                <Form.Item label="功能描述" name="description">
                    <TextArea placeholder="请输入功能描述" rows={2} />
                </Form.Item>

                <Form.Item label="命令" name="command">
                    <Input placeholder="请输入命令" />
                </Form.Item>

                <Form.Item label="接口地址" name="url">
                    <Input placeholder="请输入接口地址" />
                </Form.Item>

                <Form.Item label="配置信息" name="env" rules={rules.env}>
                    <Input placeholder="请输入配置信息" />
                </Form.Item>

                <Form.Item label="查询路径" name="queryPath" rules={rules.queryPath}>
                    <Input placeholder="请输入查询路径" />
                </Form.Item>

                <Form.Item label="执行路径" name="executePath" rules={rules.executePath}>
                    <Input placeholder="请输入执行路径" />
                </Form.Item>

                <Form.Item label="工作组" name="groupId" rules={rules.groupId}>
                    <Select placeholder="请选择工作组" notFoundContent="暂无工作组">
                        {joinedWorkGroupList.map((item) => (
                            <Option key={item.id} value={item.id}>
                                <span className={styles.groupId}>#{item.id} </span>
                                {`${item.name} (${item.business})`}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(McpServerForm);
