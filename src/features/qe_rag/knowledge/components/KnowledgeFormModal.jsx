import { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, message } from 'antd';
import { getEmbddingModels } from 'COMMON/api/qe_rag/model';
import { getAllGroupsList, getJoinedList } from 'COMMON/api/qe_rag/workgroup';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import { create, edit } from 'COMMON/api/qe_rag/book';
// import dict from '../constants/dict';
import styles from 'FEATURES/qe_rag/common.module.less';
import { dict } from 'FEATURES/qe_rag/const.js';

const { Option, OptGroup } = Select;
const { TextArea } = Input;

const KnowledgeFormModal = ({
    visible,
    onCancel,
    editingBook,
    onSuccess,
    joinedWorkGroupList,
    embeddingModelList
}) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [models, setModels] = useState([]);
    // 编辑时填充表单数据
    useEffect(() => {
        if (visible && editingBook) {
            // 解析embeddingRule字符串
            let embeddingRule = editingBook.embeddingRule;
            if (typeof embeddingRule === 'string') {
                try {
                    embeddingRule = JSON.parse(embeddingRule);
                } catch (e) {
                    embeddingRule = { delimiter: [], chunkTokenNum: 600 };
                }
            }

            form.setFieldsValue({
                ...editingBook,
                embeddingRule
            });
        } else if (visible && !editingBook) {
            // 新建时设置默认值
            form.setFieldsValue({
                name: '',
                description: '',
                embeddingModelId: null,
                parseId: 1,
                groupId: null,
                embeddingRule: {
                    delimiter: [],
                    chunkTokenNum: 600
                }
            });
        }
    }, [visible, editingBook, form]);

    // 处理保存
    const handleSave = async (values) => {
        setLoading(true);
        try {
            const apiMethod = editingBook?.id ? edit : create;
            const payload = {
                ...values,
                owner: 'xushixuan01',
                ...(editingBook?.id && { id: editingBook.id })
            };

            const res = await apiMethod(payload);
            message.success('操作成功');
            onSuccess?.();
            handleCancel();
        } finally {
            setLoading(false);
        }
    };

    // 取消
    const handleCancel = () => {
        form.resetFields();
        onCancel?.();
    };

    return (
        <Modal
            title={
                <div style={{ padding: '8px', fontSize: '17px' }}>
                    {editingBook ? '编辑知识库' : '新建知识库'}
                </div>
            }
            open={visible}
            onCancel={handleCancel}
            footer={null}
            width={600}
            destroyOnClose
        >
            <Form
                form={form}
                layout="horizontal"
                onFinish={handleSave}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
            >
                <Form.Item
                    label="知识库名称"
                    name="name"
                    rules={[
                        { required: true, message: '请输入知识库名称' },
                        { max: 10, message: '知识库名称不能超过10个字符' }
                    ]}
                >
                    <Input placeholder="请输入知识库名称" />
                </Form.Item>

                <Form.Item
                    label="知识库描述"
                    name="description"
                    rules={[
                        { required: true, message: '请填写知识库描述' },
                        { max: 100, message: '描述不能超过100个字符' }
                    ]}
                >
                    <Input placeholder="输入知识库描述" />
                </Form.Item>

                <Form.Item
                    label="切词模型"
                    name="embeddingModelId"
                    rules={[{ required: true, message: '请选择切词模型' }]}
                >
                    <Select placeholder="请选择切词模型">
                        {embeddingModelList?.map((model) => (
                            <Option key={model.id} value={model.id}>
                                {`${model.platform}-${model.name}`}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="切词类型"
                    name="parseId"
                    rules={[{ required: true, message: '请选择切词类型' }]}
                >
                    <Select placeholder="请选择切词类型">
                        {dict.parserOptions.map((option) => (
                            <Option key={option.value} value={option.value}>
                                {option.label}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="切片分隔符"
                    name={['embeddingRule', 'delimiter']}
                    rules={[{ required: true, message: '请指定切词分隔符' }]}
                    tooltip='按照指定的标识符切分文本,可以有多个分割符'
                >
                    <Select mode="multiple" placeholder="切词分隔符，可以多个">
                        {dict.delimiters.map((group) => (
                            <OptGroup key={group.label} label={group.label}>
                                {group.options.map((option) => (
                                    <Option key={option.value} value={option.value}>
                                        {option.label}
                                    </Option>
                                ))}
                            </OptGroup>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="最大切片长度"
                    name={['embeddingRule', 'chunkTokenNum']}
                    rules={[{ required: true, message: '请输入最大切片长度' }]}
                    tooltip="切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简"
                >
                    <Input placeholder="最大切片长度" type="number" />
                </Form.Item>

                <Form.Item
                    label="工作组"
                    name="groupId"
                    rules={[{ required: true, message: '请选择工作组' }]}
                >
                    <Select placeholder="请选择工作组" notFoundContent="暂无工作组">
                        {joinedWorkGroupList.map((item) => (
                            <Option key={item.id} value={item.id}>
                                <span className={styles.groupId}>#{item.id} </span>
                                {`${item.name} (${item.business})`}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    style={{ marginBottom: 0, textAlign: 'right' }}
                    wrapperCol={{ span: 24 }}
                >
                    <Button onClick={handleCancel} style={{ marginRight: 8 }}>
                        取消
                    </Button>
                    <Button type="primary" htmlType="submit" loading={loading}>
                        保存
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(KnowledgeFormModal);
