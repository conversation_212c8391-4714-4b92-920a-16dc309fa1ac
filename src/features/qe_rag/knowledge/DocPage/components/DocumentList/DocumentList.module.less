.documentItem:hover {
  background-color: #f5f5f5 !important;
}

.documentItemSelected:hover {
  background-color: #1677ff !important;
}

.documentListCard {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.listMainContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.listScrollContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.tableHeader {
  display: flex;
  padding: 8px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  font-size: 14px;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.tableHeaderId {
  width: 40px;
  text-align: center;
}

.tableHeaderTitle {
  flex: 1;
}

.tableHeaderType {
  width: 60px;
  text-align: center;
}

.tableHeaderStatus {
  width: 80px;
  text-align: center;
}

.tableHeaderActions {
  width: 40px;
}

// Document list item styles
.documentListItem {
  cursor: pointer;
  padding: 5px 20px;
  border-radius: 8px;
  margin-bottom: 4px;
  border: none;
  transition: all 0.2s ease;
}

.documentListItemNormal {
  background-color: transparent;
  color: #333333;
  box-shadow: none;
}

.documentListItemSelected {
  background-color: #1677ff;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

.documentItemContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.documentItemLeft {
  flex: 1;
}

.documentItemTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
}

.documentItemTitleNormal {
  color: #333333;
  font-weight: 400;
}

.documentItemTitleSelected {
  color: #ffffff;
  font-weight: 500;
}

.documentIdBadge {
  background-color: #f5f5f5;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

.documentItemRight {
  display: flex;
  align-items: center;
  gap: 8px;
}

.documentTypeTag {
  min-width: 50px;
  text-align: center;
  display: inline-block;
}

.dropdownItemDisable {
  color: #faad14;
}

.dropdownItemEnable {
  color: #52c41a;
}

.dropdownItemDelete {
  color: #ff4d4f;
}

.dropdownIconMargin {
  margin-right: 8px;
}

.moreIcon {
  color: #999999;
}

.moreIconSelected {
  color: #ffffff;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}
