// Right panel card
.rightPanelCard {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Tabs styles
.tabsContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Tab pane content
.tabPaneContent {
  padding: 20px;
  height: calc(100vh - 230px);
  overflow-y: auto;
}

.tabPaneContentEdit {
  padding: 20px;
  height: calc(100vh - 260px);
  overflow-y: auto;
}

// Empty state
.emptyState {
  margin-top: 100px;
}
