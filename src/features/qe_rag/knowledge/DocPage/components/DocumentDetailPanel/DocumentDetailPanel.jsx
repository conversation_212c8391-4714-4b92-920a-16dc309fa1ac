import React from 'react';
import { Card, Tabs, Spin, Empty } from 'antd';
import DocumentOverview from '../DocumentOverview/DocumentOverview';
import DocumentDetailForm from '../DocumentDetailForm/DocumentDetailForm';
import styles from './DocumentDetailPanel.module.less';

const { TabPane } = Tabs;

const DocumentDetailPanel = ({
    selectedDoc,
    documentDetail,
    detailLoading,
    editLoading,
    chunkPagination,
    onChunkPageChange,
    getCurrentChunks,
    onDocumentSave
}) => {
    return (
        <Card className={styles.rightPanelCard}>
            {selectedDoc ? (
                <Tabs
                    defaultActiveKey="overview"
                    size="small"
                    className={styles.tabsContainer}
                    tabBarStyle={{
                        margin: 0,
                        paddingLeft: '20px',
                        paddingRight: '20px'
                    }}
                >
                    <TabPane tab="概览" key="overview">
                        <div className={styles.tabPaneContent}>
                            <Spin spinning={detailLoading}>
                                <DocumentOverview
                                    documentDetail={documentDetail}
                                    chunkPagination={chunkPagination}
                                    onChunkPageChange={onChunkPageChange}
                                    getCurrentChunks={getCurrentChunks}
                                />
                            </Spin>
                        </div>
                    </TabPane>

                    <TabPane tab="编辑" key="detail">
                        <div className={styles.tabPaneContentEdit}>
                            <Spin spinning={detailLoading}>
                                <DocumentDetailForm
                                    documentDetail={documentDetail}
                                    onSave={onDocumentSave}
                                    loading={editLoading}
                                />
                            </Spin>
                        </div>
                    </TabPane>
                </Tabs>
            ) : (
                <Empty
                    image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
                    className={styles.emptyState}
                    description={<div>请选择一个文档查看详情</div>}
                />
            )}
        </Card>
    );
};

export default DocumentDetailPanel;
