import React from 'react';
import { <PERSON>, Row, Col, Space, Button, Tag, Typography } from 'antd';
import { PlusOutlined, UserOutlined, CalendarOutlined } from '@ant-design/icons';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import styles from './KnowledgeBaseHeader.module.less';

const { Text } = Typography;

const KnowledgeBaseHeader = ({
    knowledgeBaseDefaults,
    allWorkGroupList,
    onAddDocument
}) => {
    const navigate = useNavigate();

    // 处理返回按钮点击
    const handleGoBack = () => {
        navigate(
            stringifyUrl({
                url: '/qe_rag/knowledge'
            })
        );
    };

    // 格式化工作组信息显示
    const formatGroupInfo = (groupId) => {
        if (!groupId) {
            return '-';
        }

        // 在工作组列表中查找对应的工作组
        const group = allWorkGroupList?.find((g) => g.id === groupId);

        return group ? (
            <span>
                <span className={styles.groupIdBadge}>
                    {groupId}
                </span>
                {group.name}
            </span>
        ) : (
            '-'
        );
    };

    return (
        <Card className={styles.knowledgeBaseCard}>
            <Row align="middle" justify="space-between">
                <Col>
                    <Space size="large">
                        {/* 返回按钮 */}
                        <Button
                            type="text"
                            icon={
                                <span className={styles.backButtonIcon}>←</span>
                            }
                            onClick={handleGoBack}
                            className={styles.backButton}
                        >
                            返回
                        </Button>
                        <div>
                            <div className={styles.headerInfo}>
                                <div>
                                    <Tag color="blue">知识库</Tag>
                                </div>

                                <div className={styles.knowledgeBaseName}>
                                    {knowledgeBaseDefaults?.name}
                                </div>
                            </div>

                            <Space className={styles.headerSpace}>
                                <Text className={styles.headerText}>
                                    <UserOutlined /> 工作组：
                                    {formatGroupInfo(knowledgeBaseDefaults?.groupId)}
                                </Text>
                                <Text className={styles.headerTextWithMargin}>
                                    <CalendarOutlined /> 创建时间:{' '}
                                    {knowledgeBaseDefaults?.createAt}
                                </Text>
                            </Space>
                        </div>
                    </Space>
                </Col>
                <Col>
                    <Button
                        type="primary"
                        size="large"
                        icon={<PlusOutlined />}
                        onClick={onAddDocument}
                        className={styles.addDocumentButton}
                    >
                        添加文档
                    </Button>
                </Col>
            </Row>
        </Card>
    );
};

export default KnowledgeBaseHeader;
