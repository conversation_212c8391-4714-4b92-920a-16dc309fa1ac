// Knowledge base card
.knowledgeBaseCard {
  margin-bottom: 10px;
  background: linear-gradient(135deg, #4F8FF7 0%, #3875f0 100%);
  border: none;
}

// Back button styles
.backButtonIcon {
  color: white;
  font-size: 18px;
}

.backButton {
  color: white;
  font-size: 16px;
  padding: 4px 8px;
}

// Header info styles
.headerInfo {
  display: flex;
  align-items: center;
}

.knowledgeBaseName {
  color: white;
  margin: 0;
}

.headerSpace {
  margin-top: 8px;
}

.headerText {
  color: rgba(255,255,255,0.8);
}

.headerTextWithMargin {
  color: rgba(255,255,255,0.8);
  margin-left: 5px;
}

// Add document button
.addDocumentButton {
  background-color: rgba(255,255,255,0.3);
  border-color: rgba(255,255,255,0.6);
}

// Group info badge
.groupIdBadge {
  background-color: #6fa3f5a6;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
  border: 1px solid #6fa3f5;
}
