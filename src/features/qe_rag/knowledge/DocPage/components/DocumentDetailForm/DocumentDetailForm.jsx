import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Form, Button, Switch, Space, Upload, Typography, message, Input } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { upload } from 'COMMON/api/qe_rag/document';
import { getQueryParams } from 'COMMON/utils/utils';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import {
    CronExpressionInput,
    VirtualDocumentFields,
    LinkDocumentFields,
    BasicEmbeddingFields
} from './FormFields';

const { Text, Title } = Typography;
const { Dragger } = Upload;

const DocumentDetailForm = forwardRef(({
    // 通用props
    onSave,
    onCancel,
    loading = false,
    modelsLoading = false,
embeddingModelList = [],
    // 编辑模式props
    documentDetail = null,

    // 创建模式props
    mode = 'edit', // 'edit' | 'create'
    documentType = '', // 创建模式下的文档类型
    title = '', // 表单标题
    showActions = true, // 是否显示操作按钮
    defaultEditing = false, // 编辑模式下是否默认进入编辑状态
    initialValues = {}, // 初始值

    // 创建模式特有props
    files = [], // 文件列表
    onFileChange = () => {} // 文件变化回调
}, ref) => {
    const [form] = Form.useForm();
    const [isEditing, setIsEditing] = useState(mode === 'create' || defaultEditing);
    const [fileList, setFileList] = useState(files || []);

    // 获取知识库ID
    const query = getQueryParams();
    const knowledgeBaseId = query?.knowledgeId;

    // 获取当前文档类型
    const currentDocumentType = mode === 'create' ? documentType : documentDetail?.type;

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
        handleSubmit: handleSave
    }));

    // 文件上传处理
    const handleUpload = async ({ file, onSuccess, onError }) => {
        try {
            if (!knowledgeBaseId) {
                throw new Error('知识库ID不能为空，请检查URL参数');
            }

            // 创建FormData
            const formData = new FormData();
            formData.append('files', file);
            formData.append('knowledgeBaseId', knowledgeBaseId);

            // 调用上传API
            const result = await upload(formData);

            if (result && Array.isArray(result) && result.length > 0) {
                const uploadedFile = result[0];

                // 保存bosUrl到文件对象
                file.bosUrl = uploadedFile.bosUrl;
                file.uploadResponse = uploadedFile;

                // 更新文件列表
                const newFileList = [
                    {
                        uid: file.uid,
                        name: file.name,
                        status: 'done',
                        url: uploadedFile.bosUrl,
                        response: uploadedFile,
                        bosUrl: uploadedFile.bosUrl
                    }
                ];

                setFileList(newFileList);

                // 更新表单中的bosUrl字段
                form.setFieldsValue({
                    bosUrl: uploadedFile.bosUrl
                });

                // 如果是创建模式，通知父组件文件变化
                if (mode === 'create' && onFileChange) {
                    onFileChange({ fileList: newFileList });
                }

                message.success(`文件 "${file.name}" 上传成功`);
                onSuccess(result);
            } else if (result && !Array.isArray(result)) {
                throw new Error(result.message || '文件上传失败');
            } else {
                throw new Error('文件上传失败：服务器返回空结果');
            }
        } catch (error) {
            console.error('文件上传错误:', error);

            let errorMessage = '文件上传失败';
            if (error.message) {
                if (error.message.includes('Unexpected token')) {
                    errorMessage = '服务器返回格式错误，请检查服务器状态';
                } else if (error.message.includes('traceId')) {
                    errorMessage = '服务器内部错误，请稍后重试';
                } else {
                    errorMessage = error.message;
                }
            }

            message.error(`文件 "${file.name}" 上传失败: ${errorMessage}`);
            onError(error);
        }
    };

    // 文件上传前的验证
    const handleBeforeUpload = (file) => {
        // 创建模式下总是允许上传，编辑模式下需要在编辑状态才能上传
        if (mode === 'edit' && !isEditing) {
            return false;
        }

        const isValidType = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'application/json',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ].includes(file.type);

        if (!isValidType) {
            message.error(
                `不支持的文件格式: ${file.type}。只支持 .doc | .txt | .docx | .pdf | .json | .excel 格式的文件`
            );
            return Upload.LIST_IGNORE;
        }

        const isLt50M = file.size / 1024 / 1024 < 50;
        if (!isLt50M) {
            message.error('文件大小不能超过50MB');
            return Upload.LIST_IGNORE;
        }

        return true;
    };

    // 文件变化处理
    const handleFileChange = ({ fileList }) => {
        setFileList(fileList);

        // 如果是创建模式，通知父组件
        if (mode === 'create' && onFileChange) {
            onFileChange({ fileList });
        }
    };

    // 文件移除处理
    const handleRemove = (file) => {
        if (mode === 'edit' && !isEditing) {
            return false;
        }

        const newFileList = fileList.filter((f) => f.uid !== file.uid);
        setFileList(newFileList);

        // 清空表单中的bosUrl
        form.setFieldsValue({ bosUrl: '' });

        // 如果是创建模式，通知父组件
        if (mode === 'create' && onFileChange) {
            onFileChange({ fileList: newFileList });
        }

        return true;
    };

    // 初始化表单数据
    useEffect(() => {
        if (mode === 'edit' && documentDetail) {
            // 编辑模式：从documentDetail初始化
            const embeddingRule = documentDetail.embeddingRule
                ? JSON.parse(documentDetail.embeddingRule)
                : {};
            const formValues = {
                embeddingModelId: documentDetail.embeddingModelId
            };

            // 根据文档类型设置不同的字段
            if (documentDetail.type === 'virtual') {
                formValues.title = documentDetail.title;
            } else if (documentDetail.type === 'link') {
                formValues.title = documentDetail.title;
                formValues.sourceUrl = documentDetail.sourceUrl || '';
                formValues.parseId = documentDetail.parseId;
                formValues.delimiter = embeddingRule.delimiter || [];
                formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
                formValues.cronOpen = documentDetail.cronOpen === 1;
                formValues.cronExpression = documentDetail.cronExpression || '';
            } else {
                formValues.parseId = documentDetail.parseId;
                formValues.delimiter = embeddingRule.delimiter || [];
                formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
                formValues.bosUrl = documentDetail.bosUrl || '';
            }

            form.setFieldsValue(formValues);

            // 设置文件列表
            if (documentDetail.bosUrl) {
                setFileList([
                    {
                        uid: documentDetail.id,
                        name: documentDetail.title,
                        status: 'done',
                        url: documentDetail.bosUrl,
                        type: documentDetail.type
                    }
                ]);
            } else {
                setFileList([]);
            }
        } else if (mode === 'create') {
            // 创建模式：使用初始值
            form.setFieldsValue(initialValues);
            setFileList(files || []);
        }
    }, [mode, documentDetail?.id]);

    // 进入编辑模式
    const handleEdit = () => {
        setIsEditing(true);
    };

    // 取消编辑/取消操作
    const handleCancel = () => {
        if (mode === 'edit') {
            setIsEditing(false);
            // 重置表单到原始值
            if (documentDetail) {
                const embeddingRule = documentDetail.embeddingRule
                    ? JSON.parse(documentDetail.embeddingRule)
                    : {};
                const formValues = {
                    embeddingModelId: documentDetail.embeddingModelId
                };

                // 根据文档类型重置不同的字段
                if (documentDetail.type === 'virtual') {
                    formValues.title = documentDetail.title;
                } else if (documentDetail.type === 'link') {
                    formValues.title = documentDetail.title;
                    formValues.sourceUrl = documentDetail.sourceUrl || '';
                    formValues.parseId = documentDetail.parseId;
                    formValues.delimiter = embeddingRule.delimiter || [];
                    formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
                    formValues.cronOpen = documentDetail.cronOpen === 1;
                    formValues.cronExpression = documentDetail.cronExpression || '';
                } else {
                    formValues.parseId = documentDetail.parseId;
                    formValues.delimiter = embeddingRule.delimiter || [];
                    formValues.chunkTokenNum = embeddingRule.chunkTokenNum || 600;
                }

                form.setFieldsValue(formValues);
            }
        }
        onCancel?.();
    };

    // 保存/提交处理
    const handleSave = async () => {
        try {
            const values = await form.validateFields();

            if (mode === 'edit') {
                // 编辑模式：构建更新数据
                const updateData = {
                    id: documentDetail.id,
                    embeddingModelId: values.embeddingModelId,
                    type: documentDetail.type,
                    bosUrl: documentDetail.bosUrl,
                    knowledgeBaseId: documentDetail.knowledgeBaseId,
                    owner: documentDetail.owner
                };

                // 根据文档类型添加不同的字段
                if (documentDetail.type === 'virtual') {
                    updateData.title = values.title;
                } else if (documentDetail.type === 'link') {
                    updateData.title = values.title;
                    updateData.sourceUrl = values.sourceUrl || '';
                    updateData.parseId = values.parseId;
                    updateData.embeddingRule = JSON.stringify({
                        delimiter: values.delimiter || [],
                        chunkTokenNum: values.chunkTokenNum || 600
                    });
                    updateData.cronOpen = values.cronOpen ? 1 : 0;
                    updateData.cronExpression = values.cronExpression || '';
                } else {
                    updateData.title = documentDetail.title;
                    updateData.parseId = values.parseId;
                    updateData.embeddingRule = JSON.stringify({
                        delimiter: values.delimiter || [],
                        chunkTokenNum: values.chunkTokenNum || 600
                    });
                    if (values.bosUrl) {
                        updateData.bosUrl = values.bosUrl;
                    }
                }

                await onSave(updateData);
                setIsEditing(false);
            } else {
                // 创建模式：直接传递表单值和文件列表
                await onSave({
                    values,
                    fileList,
                    documentType
                });
            }
        } catch (error) {
            console.error('保存失败:', error);
            if (mode === 'create') {
                message.error('请检查表单输入');
            }
        }
    };



    // 编辑模式下如果没有文档详情，显示错误信息
    if (mode === 'edit' && !documentDetail) {
        return (
            <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">无法获取文档详情</Text>
            </div>
        );
    }

    return (
        <div>
            {/* 标题 */}
            {title && (
                <div style={{ marginBottom: '24px' }}>
                    <Title level={4}>{title}</Title>
                </div>
            )}

            {/* 操作按钮 */}
            {showActions && (
                <div style={{ marginBottom: '24px', textAlign: 'right' }}>
                    {mode === 'edit' && !isEditing ? (
                        <Button type="primary" onClick={handleEdit}>
                            编辑
                        </Button>
                    ) : mode === 'edit' && isEditing ? (
                        <Space>
                            <Button onClick={handleCancel}>取消</Button>
                            <Button type="primary" loading={loading} onClick={handleSave}>
                                保存
                            </Button>
                        </Space>
                    ) : mode === 'create' ? (
                        <Space>
                            <Button onClick={handleCancel}>取消</Button>
                            <Button type="primary" loading={loading} onClick={handleSave}>
                                确定
                            </Button>
                        </Space>
                    ) : null}
                </div>
            )}

            {/* 表单 */}
            <Form
                form={form}
                layout="vertical"
                disabled={mode === 'edit' && !isEditing}
            >
                {/* 根据文档类型显示不同的表单字段 */}
                {currentDocumentType === 'virtual' && (
                    <VirtualDocumentFields
                        models={embeddingModelList || []}
                        // modelsLoading={modelsLoading}
                    />
                )}

                {currentDocumentType === 'link' && (
                    <>
                        <LinkDocumentFields
                            models={embeddingModelList}
                            // modelsLoading={modelsLoading}
                            documentType={currentDocumentType}
                        />
                        <Form.Item label="定时任务：" name="cronOpen" valuePropName="checked">
                            <Switch />
                        </Form.Item>
                        <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) =>
                                prevValues.cronOpen !== currentValues.cronOpen
                            }
                        >
                            {({ getFieldValue }) => {
                                return getFieldValue('cronOpen') ? (
                                    <CronExpressionInput />
                                ) : null;
                            }}
                        </Form.Item>
                    </>
                )}

                {(currentDocumentType === 'upload' ||
                    currentDocumentType === 'pdf' ||
                    currentDocumentType === 'xlsx' ||
                    currentDocumentType === 'docx' ||
                    currentDocumentType === 'txt' ||
                    currentDocumentType === 'json' ||
                    currentDocumentType === 'excel') && (
                    <>
                        {/* 文件上传类型：显示选择文件、模型、解析、切词 */}
                        {mode === 'create' && (
                            <>
                                {/* 隐藏的文件验证字段 */}
                                <Form.Item
                                    name="files"
                                    rules={[
                                        {
                                            validator: () => {
                                                if (!fileList || fileList.length === 0) {
                                                    return Promise.reject(
                                                        new Error('请选择需要导入的文件')
                                                    );
                                                }
                                                return Promise.resolve();
                                            }
                                        }
                                    ]}
                                    style={{ display: 'none' }}
                                >
                                    <Input />
                                </Form.Item>
                            </>
                        )}

                        <Form.Item label="选择文件" required>
                            <Dragger
                                multiple={mode === 'create'}
                                disabled={mode === 'edit' && !isEditing}
                                fileList={fileList}
                                beforeUpload={handleBeforeUpload}
                                customRequest={handleUpload}
                                onRemove={handleRemove}
                                onChange={(info) => {
                                    handleFileChange(info);
                                    // 触发隐藏字段的验证（仅创建模式）
                                    if (mode === 'create') {
                                        form.setFieldsValue({
                                            files: info.fileList.length > 0 ? 'hasFiles' : ''
                                        });
                                    }
                                }}
                                style={{ marginBottom: 16 }}
                            >
                                <p className="ant-upload-drag-icon">
                                    <UploadOutlined
                                        style={{
                                            fontSize: 48,
                                            color: mode === 'edit' && !isEditing ? '#ccc' : '#999'
                                        }}
                                    />
                                </p>
                                <p className="ant-upload-text">
                                    {mode === 'create' || (mode === 'edit' && isEditing) ? (
                                        <>
                                            将文档拖动至此处，或{' '}
                                            <span style={{ color: '#1890ff' }}>点击上传</span>
                                        </>
                                    ) : (
                                        <span style={{ color: '#999' }}>
                                            当前文件：{documentDetail?.title}
                                        </span>
                                    )}
                                </p>
                                {(mode === 'create' || (mode === 'edit' && isEditing)) && (
                                    <p className="ant-upload-hint" style={{ color: '#e6a23c' }}>
                                        {mode === 'create' ? '单次上传文档数量为5个；' : ''}
                                        单个文件小于50M；支持.doc | .txt | .docx | .pdf | .json |
                                        .excel
                                    </p>
                                )}
                            </Dragger>
                        </Form.Item>

                        <BasicEmbeddingFields
                            models={embeddingModelList}
                            // modelsLoading={modelsLoading}
                            documentType={currentDocumentType}
                        />
                    </>
                )}
            </Form>
        </div>
    );
});


export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(DocumentDetailForm);