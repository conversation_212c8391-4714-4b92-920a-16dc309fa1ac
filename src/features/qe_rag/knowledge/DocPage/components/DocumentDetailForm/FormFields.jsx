import { Form, Input, Select } from 'antd';
import { isValidCron } from 'cron-validator';
import { dict } from '../../const';

// 验证Cron表达式的函数
const validateCronExpression = (_, value) => {
    if (!value) {
        return Promise.resolve();
    }

    const isOk = isValidCron(value, {
        seconds: true,
        alias: true,
        allowBlankDay: true,
        allowSevenAsSunday: true
    });

    if (!isOk) {
        return Promise.reject(new Error('Cron表达式格式错误，请检查语法'));
    }
    return Promise.resolve();
};

const { Option, OptGroup } = Select;

/**
 * 文档表单字段组件集合
 * 提供可复用的表单字段组件，避免重复代码
 */

// 切词模型选择器
export const EmbeddingModelSelect = ({
    models = [],
    required = true,
    label = '切词模型',
    name = 'embeddingModelId',
    placeholder = '请选择切词模型'
}) => (
    <Form.Item
        label={label}
        name={name}
        rules={required ? [{ required: true, message: '请选择切词模型' }] : []}
        required={required}
    >
        <Select placeholder={placeholder}>
            {models.map((model) => (
                <Option key={model.id} value={model.id}>
                    {model.platform}-{model.name}
                </Option>
            ))}
        </Select>
    </Form.Item>
);

// 切词类型选择器
export const ParseTypeSelect = ({
    required = true,
    label = '切词类型',
    name = 'parseId',
    placeholder = '请选择切词类型'
}) => (
    <Form.Item
        label={label}
        name={name}
        rules={required ? [{ required: true, message: '请选择切词类型' }] : []}
        required={required}
    >
        <Select placeholder={placeholder}>
            {dict.parserOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                    {option.label}
                </Option>
            ))}
        </Select>
    </Form.Item>
);

// 切片分隔符选择器
export const DelimiterSelect = ({
    required = false,
    label = <>切片分隔符 &nbsp;&nbsp;</>,
    name = 'delimiter',
    placeholder = '请选择切词分隔符，可以多个',
    tooltip = '按照指定的标识符切分文本,可以有多个分割符'
}) => (
    <Form.Item
        label={label}
        tooltip={tooltip}
        name={name}
        rules={required ? [{ required: true, message: '请指定切词分隔符' }] : []}
        required={required}
    >
        <Select mode="multiple" placeholder={placeholder}>
            {dict.delimiters.map((group) => (
                <OptGroup key={group.label} label={group.label}>
                    {group.options.map((option) => (
                        <Option key={option.value} value={option.value}>
                            {option.label}
                        </Option>
                    ))}
                </OptGroup>
            ))}
        </Select>
    </Form.Item>
);

// 最大切片长度输入框
export const ChunkTokenNumInput = ({
    required = false,
    label = <>最大切片长 &nbsp;&nbsp;</>,
    name = 'chunkTokenNum',
    placeholder = '最大切片长度',
    tooltip = '切片长度越大，召回的上下文越丰富。长度越小，召回的信息越精简'
}) => (
    <Form.Item
        label={label}
        tooltip={tooltip}
        name={name}
        rules={
            required
                ? [
                      { required: true, message: '请输入最大切片长度' },
                      { pattern: /^\d+$/, message: '请输入有效的数字' }
                  ]
                : []
        }
        required={required}
    >
        <Input placeholder={placeholder} type="number" />
    </Form.Item>
);

// 文档标题输入框
export const DocumentTitleInput = ({
    required = true,
    label = '文档标题',
    name = 'title',
    placeholder = '请输入文档标题'
}) => (
    <Form.Item
        label={label}
        name={name}
        rules={required ? [{ required: true, message: '请输入文档标题' }] : []}
        required={required}
    >
        <Input placeholder={placeholder} />
    </Form.Item>
);

// URL链接输入框
export const SourceUrlInput = ({
    required = true,
    label = 'URL 链接',
    name = 'sourceUrl',
    placeholder = '文档的在线访问地址 URL'
}) => (
    <Form.Item
        label={label}
        name={name}
        rules={required ? [{ required: true, message: '请输入文档链接' }] : []}
        required={required}
    >
        <Input placeholder={placeholder} />
    </Form.Item>
);

// Cron表达式输入框
export const CronExpressionInput = ({
    required = false,
    label = <span>Cron 表达式：</span>,
    name = 'cronExpression',
    placeholder = '请输入Cron表达式，例如：0 0 12 * * ?',
    tooltip = '请输入Cron表达式，例如：0 0 12 * * ?'
}) => (
    <Form.Item
        label={label}
        tooltip={tooltip}
        name={name}
        rules={[{ validator: validateCronExpression }]}
        required={required}
    >
        <Input placeholder={placeholder} />
    </Form.Item>
);

/**
 * 组合字段组件 - 用于常见的字段组合
 */

// 基础切词配置字段组合（模型 + 类型 + 分隔符 + 长度）
export const BasicEmbeddingFields = ({ models, documentType }) => (
    <>
        <EmbeddingModelSelect models={models} />
        <ParseTypeSelect />
        <DelimiterSelect required={documentType !== 'virtual'} />
        <ChunkTokenNumInput required={documentType !== 'virtual'} />
    </>
);

// 虚拟文档字段组合（标题 + 模型）
export const VirtualDocumentFields = ({ models }) => (
    <>
        <DocumentTitleInput />
        <EmbeddingModelSelect models={models} />
    </>
);

// 链接文档字段组合（标题 + URL + 基础切词配置）
export const LinkDocumentFields = ({ models, documentType }) => (
    <>
        <DocumentTitleInput />
        <SourceUrlInput />
        <BasicEmbeddingFields models={models} documentType={documentType} />
    </>
);
