import React from 'react';
import { Typography, Descriptions, Tooltip, Empty } from 'antd';
import DocumentChunks from '../DocumentChunks/DocumentChunks';
import styles from './DocumentOverview.module.less';

const { Title, Text } = Typography;

const DocumentOverview = ({
    documentDetail,
    chunkPagination,
    onChunkPageChange,
    getCurrentChunks
}) => {
    if (!documentDetail) {
        return <Empty description="加载文档详情中..." />;
    }

    return (
        <div className={styles.documentDetailContainer}>
            {/* 基本信息 */}
            <div className={styles.basicInfoSection}>
                <Title level={5} className={styles.sectionTitle}>
                    基本信息
                </Title>

                <Descriptions
                    title=""
                    className={styles.descriptionsContainer}
                    column={1}
                    size="small"
                >
                    <Descriptions.Item label="文档ID">
                        {documentDetail.id}
                    </Descriptions.Item>

                    <Descriptions.Item
                        label="文档标题"
                        className={styles.descriptionItem}
                    >
                        <Tooltip title={documentDetail.title}>
                            <div className={styles.descriptionItemContent}>
                                {documentDetail.title}
                            </div>
                        </Tooltip>
                    </Descriptions.Item>

                    <Descriptions.Item label="文档类型">
                        {documentDetail.type}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建人">
                        {documentDetail.owner}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">
                        {documentDetail.createAt}
                    </Descriptions.Item>
                    <Descriptions.Item label="更新时间">
                        {documentDetail.updateAt}
                    </Descriptions.Item>
                </Descriptions>
            </div>

            {/* 处理消息 */}
            <div className={styles.processingMessageSection}>
                <Title level={5} className={styles.sectionTitleWithMargin}>
                    处理消息
                </Title>
                <div className={styles.processingMessageContent}>
                    <Text type="warning">{documentDetail.message}</Text>
                </div>
            </div>

            {/* 附加信息 */}
            {documentDetail.metadata && (
                <div className={styles.metadataSection}>
                    <Title level={5} className={styles.sectionTitle}>
                        附加信息
                    </Title>
                    <div className={styles.metadataContent}>
                        <Text>{documentDetail.metadata}</Text>
                    </div>
                </div>
            )}

            {/* 切片信息 */}
            <div className={styles.chunksSection}>
                <div className={styles.chunksTitleContainer}>
                    <Title level={5} className={styles.sectionTitleChunks}>
                        切片信息
                        {documentDetail.contentList &&
                            documentDetail.contentList.length > 0 && (
                                <Text
                                    type="secondary"
                                    className={styles.chunksCount}
                                >
                                    （共 {documentDetail.contentList.length} 条）
                                </Text>
                            )}
                    </Title>
                </div>
                {documentDetail.contentList &&
                documentDetail.contentList.length > 0 ? (
                    <DocumentChunks
                        chunks={getCurrentChunks()}
                        pagination={chunkPagination}
                        onPageChange={onChunkPageChange}
                    />
                ) : (
                    <Empty description="暂无切片数据" />
                )}
            </div>
        </div>
    );
};

export default DocumentOverview;
