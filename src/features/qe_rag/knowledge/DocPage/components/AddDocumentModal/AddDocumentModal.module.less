.typeSelectorItem {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s;
}

.typeSelectorItem:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
}

.stepTitle {
    margin-bottom: 24px;
    font-size: 16px;
}

.typeContainer {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.typeTitle {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.typeDescription {
    color: #666;
    font-size: 14px;
}

.footerButton {
    margin-left: 8px;
}
