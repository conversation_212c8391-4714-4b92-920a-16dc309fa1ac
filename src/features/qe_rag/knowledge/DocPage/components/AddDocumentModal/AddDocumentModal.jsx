import { useState, useRef } from 'react';
import { Modal, Button, message } from 'antd';
import styles from './AddDocumentModal.module.less';
import { createDocument } from 'COMMON/api/qe_rag/document';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import { getQueryParams } from 'COMMON/utils/utils';
import DocumentDetailForm from '../DocumentDetailForm/DocumentDetailForm';

const AddDocumentModal = ({
    visible,
    onCancel,
    onSuccess,
    loading,
    embeddingModelList = [],
    defaultValues,
    defaultsLoading
}) => {
    const [step, setStep] = useState(1);
    const [documentType, setDocumentType] = useState('');
    const [files, setFiles] = useState([]);
    const formRef = useRef(null);
    const query = getQueryParams();
    const knowledgeBaseId = query.knowledgeId;

    // 重置状态
    const resetModal = () => {
        setStep(1);
        setDocumentType('');
        setFiles([]);
    };

    // 关闭弹窗
    const handleCancel = () => {
        resetModal();
        onCancel();
    };

    // 选择文档类型
    const handleTypeSelect = (type) => {
        setDocumentType(type);
        setStep(2);
    };

    // 返回第一步
    const handleBack = () => {
        setStep(1);
        setFiles([]);
    };

    // 文件变化处理
    const handleFileChange = ({ fileList }) => {
        setFiles(fileList);
    };

    // 表单提交处理 - 适配 DocumentDetailForm 的回调格式
    const handleSubmit = async ({ values, fileList, documentType: docType }) => {
        try {
            // 获取当前用户
            const getCurrentUser = () => {
                return sessionStorage.getItem('username') || 'current_user';
            };

            // 根据接口文档构建请求数据
            const body = {
                knowledgeBaseId: String(knowledgeBaseId),
                owner: getCurrentUser(),
                embeddingRule: {
                    delimiter: values.delimiter || ['。'],
                    chunkTokenNum: values.chunkTokenNum || 600
                },
                embeddingModelId: values.embeddingModelId || 3,
                documents: []
            };

            // 根据文档类型构建 documents 数组
            switch (docType) {
                case 'upload':
                    // 检查所有文件是否都已上传完成
                    const unuploadedFiles = fileList.filter(
                        (file) => !file.bosUrl && !file.uploadResponse?.bosUrl
                    );
                    if (unuploadedFiles.length > 0) {
                        message.error('请等待所有文件上传完成后再提交');
                        return;
                    }

                    // 文件上传类型的文档数据
                    body.documents = fileList.map((file) => {
                        // 根据文件扩展名确定类型
                        const getFileType = (fileName) => {
                            const ext = fileName.split('.').pop()?.toLowerCase();
                            switch (ext) {
                                case 'pdf':
                                    return 'pdf';
                                case 'xlsx':
                                    return 'xlsx';
                                case 'xls':
                                    return 'xls';
                                case 'docx':
                                    return 'docx';
                                case 'txt':
                                    return 'txt';
                                case 'json':
                                    return 'json';
                                case 'excel':
                                    return 'excel';
                                default:
                                    return 'pdf';
                            }
                        };

                        return {
                            title: file.name,
                            type: getFileType(file.name),
                            bosUrl: file.bosUrl || file.uploadResponse?.bosUrl || '', // 从上传响应中获取URL
                            knowledgeBaseId: String(knowledgeBaseId),
                            cronOpen: values.cronOpen || 0,
                            cronExpression: values.cronExpression || '' // 添加这一行
                        };
                    });
                    break;

                case 'link':
                    body.documents = [
                        {
                            title: values.title,
                            type: 'link',
                            sourceUrl: values.sourceUrl,
                            knowledgeBaseId: String(knowledgeBaseId),
                            cronOpen: values.cronOpen || 0,
                            cronExpression: values.cronExpression || '' // 添加这一行
                        }
                    ];
                    break;

                case 'virtual':
                    body.documents = [
                        {
                            title: values.title,
                            type: 'virtual',
                            knowledgeBaseId: String(knowledgeBaseId),
                            cronOpen: values.cronOpen || 0,
                            cronExpression: values.cronExpression || '' // 添加这一行
                        }
                    ];
                    break;
            }

            const result = await createDocument(body);

            message.success('文档添加成功');

            // 调用成功回调刷新列表并选中新创建的文档
            if (onSuccess) {
                // 获取新创建的文档ID（通常在响应数据中）
                const newDocuments = result || [];
                const firstNewDocId = newDocuments.length > 0 ? newDocuments[0].id : null;

                await onSuccess(firstNewDocId);
            }

            resetModal();
            onCancel();
        } catch (error) {
            message.error('请检查表单输入');
        }
    };

    return (
        <Modal
            title={step === 1 ? '创建知识库' : '添加文档'}
            open={visible}
            onCancel={handleCancel}
            width={800}
            zIndex={10000}
            getContainer={false}
            maskClosable={false}
            footer={
                step === 1 ? (
                    <Button onClick={handleCancel}>取消</Button>
                ) : (
                    <div>
                        <Button onClick={handleBack}>返回</Button>
                        <Button onClick={handleCancel} className={styles.footerButton}>
                            取消
                        </Button>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={() => {
                                // 触发 DocumentDetailForm 的表单提交
                                if (formRef.current) {
                                    formRef.current.handleSubmit();
                                }
                            }}
                            className={styles.footerButton}
                        >
                            确定
                        </Button>
                    </div>
                )
            }
        >
            {step === 1 ? (
                // 第一步：选择创建类型
                <div>
                    <p className={styles.stepTitle}>请选择创建类型：</p>
                    <div className={styles.typeContainer}>
                        <div
                            onClick={() => handleTypeSelect('upload')}
                            className={styles.typeSelectorItem}
                        >
                            <div className={styles.typeTitle}>
                                📁 文件上传
                            </div>
                            <div className={styles.typeDescription}>
                                上传PDF、Word、Excel等文档
                            </div>
                        </div>

                        <div
                            onClick={() => handleTypeSelect('link')}
                            className={styles.typeSelectorItem}
                        >
                            <div className={styles.typeTitle}>
                                🗄️ 知识库
                            </div>
                            <div className={styles.typeDescription}>创建结构化知识库</div>
                        </div>

                        <div
                            onClick={() => handleTypeSelect('virtual')}
                            className={styles.typeSelectorItem}
                        >
                            <div className={styles.typeTitle}>
                                ✏️ 虚拟文档
                            </div>
                            <div className={styles.typeDescription}>手动创建虚拟文档</div>
                        </div>
                    </div>
                </div>
            ) : (
                // 第二步：使用通用表单组件
                <DocumentDetailForm
                    ref={formRef}
                    mode="create"
                    documentType={documentType}
                    onSave={handleSubmit}
                    onCancel={handleBack}
                    loading={loading}
                    models={embeddingModelList}
                    modelsLoading={defaultsLoading}
                    initialValues={defaultValues}
                    files={files}
                    onFileChange={handleFileChange}
                    showActions={false} // 不显示内置的操作按钮，使用Modal的footer
                />
            )}
        </Modal>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(AddDocumentModal);
