.container {
    padding: 0;
    box-sizing: border-box;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    position: relative;
    height: calc(100vh - 60px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

// 聊天容器
.chatContainer {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    margin-bottom: 120px;
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;

        &:hover {
            background: rgba(0, 0, 0, 0.2);
        }
    }
}

// 欢迎页面样式
.welcomeContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    position: relative;
}

.welcomeContent {
    text-align: center;
    padding: 60px 40px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    max-width: 500px;
    margin: 0 20px;
    animation: fadeInUp 0.8s ease-out;
    border: 1px solid #e9ecef;
}

.welcomeIcon {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: bounce 2s infinite;

    img {
        width: 50px;
        height: 50px;
        object-fit: contain;
    }
}

.welcomeTitle {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.3;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcomeSubtitle {
    font-size: 18px;
    color: #7f8c8d;
    margin: 0;
    font-weight: 400;
    line-height: 1.5;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes floatIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

// 消息样式
.messageWrapper {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
    animation: messageSlideIn 0.3s ease-out;
    max-width: 100%;
}

.userMessageWrapper {
    justify-content: flex-end;
}

.aiMessageWrapper {
    justify-content: flex-start;
}

.messageAvatar {
    flex-shrink: 0;
    margin-top: 2px;
}

.aiAvatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #1677ff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

.messageContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    max-width: 70%;
}

.messageBubble {
    word-break: break-word;
    font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
    font-size: 15px;
    line-height: 1.6;
    padding: 12px 16px;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.userMessage {
    color: #fff;
    background: #1677ff;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.aiMessage {
    background: white;
    color: #333;
    border-bottom-left-radius: 4px;
    border: 1px solid #e9ecef;
}

.conversationItem {
    margin-bottom: 24px;

    &:last-child {
        margin-bottom: 0;
    }
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// 输入框样式
.inputContainer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background: linear-gradient(to top,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.8) 70%,
        rgba(255, 255, 255, 0) 100%);
    backdrop-filter: blur(10px);
    z-index: 100;
}

.inputWrapper {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    &:focus-within {
        box-shadow: 0 12px 40px rgba(22, 119, 255, 0.2);
        border-color: rgba(22, 119, 255, 0.3);
    }
}

// 发送按钮样式
.sendButton {
    width: 36px !important;
    height: 36px !important;
    border-radius: 50% !important;
    background: #64b5f6 !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(100, 181, 246, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;

    &:hover:not(:disabled) {
        background: #42a5f5 !important;
        transform: scale(1.05) !important;
        box-shadow: 0 4px 12px rgba(100, 181, 246, 0.4) !important;
    }

    &:active:not(:disabled) {
        transform: scale(0.95) !important;
    }

    &:disabled {
        background: #e0e0e0 !important;
        box-shadow: none !important;
        cursor: not-allowed !important;
    }

    .anticon {
        font-size: 16px !important;
        color: white !important;
    }
}

// 参考文档样式
:global(.reference-docs-container) {
    margin-bottom: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e9ecef;
}

:global(.reference-docs-header) {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    margin-bottom: 8px;
    padding: 6px 8px;
    background: #e3f2fd;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
        background: #bbdefb;
    }
}

:global(.reference-docs-title) {
    font-weight: 600;
    color: #1976d2;
    font-size: 14px;
    display: flex;
    align-items: center;
}

:global(.custom-arrow) {
    font-size: 12px;
    color: #1976d2;
    margin-left: 8px;
    display: inline-block;
    transition: transform 0.3s ease;
    position: relative;
}

:global(.arrow-down) {
    transform: rotate(0deg);
}

:global(.arrow-right) {
    transform: rotate(-90deg);
}

:global(.custom-arrow)::before {
    content: '';
    display: inline-block;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 5px solid #1976d2;
}

:global(.reference-docs-content) {
    display: flex;
    flex-direction: column;
    gap: 6px;
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

:global(.doc-link-item) {
    margin: 2px 0;
    font-size: 13px;
    padding: 6px 8px;
    background: #f8f9fa;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
        background: #e3f2fd;
    }
}

:global(.doc-link-item a) {
    color: #1677ff;
    text-decoration: none;
    font-weight: 500;

    &:hover {
        color: #0d47a1;
        text-decoration: underline;
    }
}

:global(.think-process-container) {
    margin-bottom: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e9ecef;
}

:global(.think-process-content) {
    background: white;
    padding: 12px;
    border-radius: 6px;
    color: #333;
    flex-direction: column;
    border: 1px solid #e9ecef;
    line-height: 1.6;
}

:global(.think-process-header) {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    margin-bottom: 8px;
    padding: 6px 8px;
    background: #fff3e0;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
        background: #ffe0b2;
    }
}

:global(.think-process-title) {
    font-weight: 600;
    color: #f57c00;
    font-size: 14px;
    display: flex;
    align-items: center;
}

// 响应式设计
@media (max-width: 768px) {
    .container {
        height: 100vh;
    }

    .chatContainer {
        padding: 16px;
        margin-bottom: 140px;
    }

    .animationGif {
        width: 100px;
    }

    .welcomeContent {
        padding: 40px 20px;
        margin: 0 16px;
    }

    .welcomeTitle {
        font-size: 24px;
    }

    .welcomeSubtitle {
        font-size: 16px;
    }

    .messageBubble {
        max-width: 90%;
        font-size: 14px;
        padding: 14px 16px;
    }

    .inputContainer {
        padding: 16px;
    }

    .inputWrapper {
        padding: 12px 16px;
        flex-direction: column;
        gap: 12px;

        .ant-select {
            width: 100% !important;
        }
    }

    :global(.reference-docs-container),
    :global(.think-process-container) {
        margin: 0 -8px 16px -8px;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .welcomeIcon {
        font-size: 48px;
    }

    .welcomeTitle {
        font-size: 20px;
    }

    .welcomeSubtitle {
        font-size: 14px;
    }

    .messageBubble {
        font-size: 13px;
        padding: 12px 14px;
    }

    .aiAvatar {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }
}

// 加载动画
.loadingContainer {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
}

.loadingDots {
    display: flex;
    align-items: center;
    gap: 4px;

    span {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #1677ff;
        animation: loadingDot 1.4s infinite ease-in-out both;

        &:nth-child(1) {
            animation-delay: -0.32s;
        }

        &:nth-child(2) {
            animation-delay: -0.16s;
        }

        &:nth-child(3) {
            animation-delay: 0s;
        }
    }
}

.loadingText {
    font-size: 14px;
    color: #666;
    font-style: italic;
}

@keyframes loadingDot {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.4;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}
