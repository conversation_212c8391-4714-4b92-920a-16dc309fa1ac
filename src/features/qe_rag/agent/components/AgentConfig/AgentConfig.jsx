import BasicSettings from '../BasicSettings';
import ModelSettings from '../ModelSettings';
import KnowledgeSettings from '../KnowledgeSettings';
import MCPServerSettings from '../MCPServerSettings';
import styles from './AgentConfig.module.less';

const AgentConfig = ({
    baseURL,
    agentData,
    modelOptions,
    embeddingModelOptions,
    functionCallSupported,
    onUpdateAgentData,
    onOpenAssociationDialog,
    onOpenMCPServerDialog
}) => {
    const updateField = (field, value) => {
        onUpdateAgentData({ [field]: value });
    };

    const updateModelSettings = (settings) => {
        onUpdateAgentData(settings);
    };

    return (
        <div className={styles.agentConfig}>
            <div className={styles.configContainer}>
                <BasicSettings
                    baseURL={baseURL}
                    imageUrl={agentData.imageUrl}
                    agentName={agentData.name}
                    description={agentData.description}
                    groupId={agentData.groupId}
                    onUpdateImageUrl={(value) => updateField('imageUrl', value)}
                    onUpdateAgentName={(value) => updateField('name', value)}
                    onUpdateDescription={(value) => updateField('description', value)}
                    onUpdateGroupId={(value) => updateField('groupId', value)}
                />

                <ModelSettings
                    modelOptions={modelOptions}
                    embeddingModelOptions={embeddingModelOptions}
                    modelId={agentData.modelId}
                    embeddingModelId={agentData.embeddingModelId}
                    rolePrompt={agentData.rolePrompt}
                    rulePrompt={agentData.rulePrompt}
                    responsePrompt={agentData.responsePrompt}
                    onUpdateModelId={(val) => updateField('modelId', val)}
                    onUpdateEmbeddingModelId={(val) => updateField('embeddingModelId', val)}
                    onUpdateModelSettings={updateModelSettings}
                />

                <KnowledgeSettings
                    recallCount={agentData.recallCount}
                    similarity={agentData.similarity}
                    selectedKnowledgeBases={agentData.selectedRows}
                    onUpdateRecallCount={(value) => updateField('recallCount', value)}
                    onUpdateSimilarity={(value) => updateField('similarity', value)}
                    onOpenAssociationDialog={onOpenAssociationDialog}
                />

                <MCPServerSettings
                    selectedMCPServers={agentData.flowNodes || []}
                    functionCallSupported={functionCallSupported}
                    onUpdateSelectedMCPServers={(value) => updateField('flowNodes', value)}
                    onOpenMCPServerDialog={onOpenMCPServerDialog}
                />
            </div>
        </div>
    );
};

export default AgentConfig;
