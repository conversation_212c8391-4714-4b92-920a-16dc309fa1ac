.dialogContent {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.searchSection {
    margin-bottom: 8px;
}

/* 已选服务器执行顺序展示 */
.selectedServersDisplay {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
}

.selectedServersHeader {
    padding: 8px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    color: #595959;
    font-size: 14px;
    font-weight: 500;
}

.selectedServersList {
    padding: 12px 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.selectedServerTag {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    border-radius: 6px;
    background-color: #f0f0f0;
    border: 1px solid #d9d9d9;
}

.serverOrderBadge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #1890ff;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    margin-right: 8px;
    font-weight: bold;
}

.selectedServerName {
    font-size: 13px;
    color: #262626;
}

/* 服务器列表容器 */
.serverListContainer {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
}

.serverListHeader {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    color: #595959;
    font-size: 14px;
}

.serverCount {
    color: #8c8c8c;
}

.serverListContent {
    max-height: 300px;
    overflow-y: auto;
}

.serverItem {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
        background-color: #f5f5f5;
    }
}

.serverCheckbox {
    display: flex;
    align-items: center;
    gap: 12px;
}

.customOrderCheckbox {
    width: 20px;
    height: 20px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        border-color: #1890ff;
    }

    &.isSelected {
        border-color: #1890ff;
        background-color: #1890ff;
    }
}

.checkboxNumber {
    color: white;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

.serverInfo {
    display: flex;
    align-items: center;
    flex: 1;
}

.serverName {
    font-size: 14px;
    color: #262626;
    line-height: 1.5;
}

/* 滚动条样式 */
.serverListContent::-webkit-scrollbar {
    width: 6px;
}

.serverListContent::-webkit-scrollbar-thumb {
    background-color: #bfbfbf;
    border-radius: 3px;
}

.serverListContent::-webkit-scrollbar-track {
    background-color: #f0f0f0;
}