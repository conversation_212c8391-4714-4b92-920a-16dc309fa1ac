.basicSettings {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border-color: #d1d9e0;
    }
}

.sectionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid #f0f2f5;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &::before {
        content: '👤';
        font-size: 16px;
    }
}

.settingsContent {
    padding: 24px;
}

.sectionLabel {
    font-size: 14px;
    color: #262626;
    margin-bottom: 8px;
    font-weight: 500;
}

.uploadSection {
    margin-bottom: 24px;
}

.avatarUploader {
    :global(.ant-upload) {
        width: 100% !important;
        height: 120px;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        background: #fafafa;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    :global(.ant-upload:hover) {
        border-color: #1890ff;
        background: #f0f8ff;
    }
}

.avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.uploadPlaceholder {
    text-align: center;
    color: #8c8c8c;
}

.uploadIcon {
    font-size: 32px;
    color: #bfbfbf;
    margin-bottom: 8px;
}

.uploadText {
    font-size: 14px;
    color: #595959;
    margin-bottom: 4px;
}

.uploadHint {
    font-size: 12px;
    color: #8c8c8c;
    line-height: 1.4;
}

.inputSection {
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}

.nameInput {
    width: 100%;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    
    &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
}

.feedbackTextarea {
    width: 100%;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    
    &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
}

.workgroupSelect {
    width: 100%;
    
    :global(.ant-select-selector) {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        
        &:hover {
            border-color: #1890ff;
        }
    }
    
    :global(.ant-select-focused .ant-select-selector) {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
}
