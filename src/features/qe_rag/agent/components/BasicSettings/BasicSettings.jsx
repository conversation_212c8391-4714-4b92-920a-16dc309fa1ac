import React, { useState, useEffect, useCallback } from 'react';
import { Upload, Input, Select, message } from 'antd';
import { CameraOutlined } from '@ant-design/icons';
import { getJoinedList, getAllGroupsList } from 'COMMON/api/qe_rag/workgroup';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import ragModel from 'COMMON/models/qe_rag/ragModel';

import styles from './BasicSettings.module.less';

const { TextArea } = Input;

const BasicSettings = ({
    baseURL,
    imageUrl,
    agentName,
    description,
    groupId,
    onUpdateImageUrl,
    onUpdateAgentName,
    onUpdateDescription,
    onUpdateGroupId,
    joinedWorkGroupList
}) => {
    const [selectedGroupId, setSelectedGroupId] = useState(null);

    const handleAvatarSuccess = (response) => {
        onUpdateImageUrl(response.data);
    };

    const beforeAvatarUpload = (file) => {
        if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
            message.error('头像图片必须是JPG或PNG格式!');
            return false;
        }
        return true;
    };

    const handleGroupChange = (value) => {
        setSelectedGroupId(value);
        onUpdateGroupId(value);
    };

    return (
        <div className={styles.basicSettings}>
            <div className={styles.sectionTitle}>基本信息</div>

            <div className={styles.settingsContent}>
                <div className={styles.sectionLabel}>应用头像11</div>
                <div className={styles.uploadSection}>
                    <Upload
                        name="avatar"
                        listType="picture-card"
                        className={styles.avatarUploader}
                        showUploadList={false}
                        action={baseURL}
                        beforeUpload={beforeAvatarUpload}
                        onSuccess={handleAvatarSuccess}
                    >
                        {imageUrl ? (
                            <img src={imageUrl} alt="avatar" className={styles.avatar} />
                        ) : (
                            <div className={styles.uploadPlaceholder}>
                                <CameraOutlined className={styles.uploadIcon} />
                                <div className={styles.uploadText}>点击上传头像</div>
                                <div className={styles.uploadHint}>
                                    建议尺寸：360*140像素，支持JPG、PNG格式
                                </div>
                            </div>
                        )}
                    </Upload>
                </div>

                <div className={styles.inputSection}>
                    <div className={styles.sectionLabel}>应用名</div>
                    <Input
                        value={agentName}
                        placeholder="我的应用名"
                        onChange={(e) => onUpdateAgentName(e.target.value)}
                        className={styles.nameInput}
                    />
                </div>

                <div className={styles.inputSection}>
                    <div className={styles.sectionLabel}>应用描述</div>
                    <TextArea
                        value={description}
                        rows={4}
                        placeholder="请输入应用描述"
                        onChange={(e) => onUpdateDescription(e.target.value)}
                        className={styles.feedbackTextarea}
                    />
                </div>

                <div className={styles.inputSection}>
                    <div className={styles.sectionLabel}>工作组</div>
                    <Select
                        value={selectedGroupId}
                        placeholder="请选择工作组"
                        allowClear
                        onChange={handleGroupChange}
                        className={styles.workgroupSelect}
                    >
                        {joinedWorkGroupList?.map((item) => (
                            <Select.Option key={item.id} value={item.id}>
                                {`${item.id} - ${item.name} (${item.business})`}
                            </Select.Option>
                        ))}
                    </Select>
                </div>
            </div>
        </div>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    username: state.common.base.username,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(BasicSettings);
