.modelSettings {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border-color: #d1d9e0;
    }
}

.sectionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid #f0f2f5;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &::before {
        content: '🧠';
        font-size: 16px;
        margin-right: 8px;
    }
}

.actionButton {
    font-size: 13px;
    color: #1890ff;
    padding: 0;
    height: auto;
    
    &:hover {
        color: #40a9ff;
    }
}

.settingsContent {
    padding: 24px;
}

.sectionLabel {
    font-size: 14px;
    color: #262626;
    margin-bottom: 8px;
    font-weight: 500;
}

.inputSection {
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}

.modelSelector {
    width: 100%;
    
    :global(.ant-select-selector) {
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        
        &:hover {
            border-color: #1890ff;
        }
    }
    
    :global(.ant-select-focused .ant-select-selector) {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
}

.linkButton {
    color: #1890ff;
    cursor: pointer;
    font-size: 14px;
    
    &:hover {
        color: #40a9ff;
        text-decoration: underline;
    }
}

.modalSection {
    margin-bottom: 20px;
    
    &:last-child {
        margin-bottom: 0;
    }
}

.modalLabel {
    font-size: 14px;
    color: #262626;
    margin-bottom: 8px;
    font-weight: 500;
}
