import React, { useState, useEffect } from 'react';
import { Select, Button, Modal, Input, message } from 'antd';
import { EditOutlined } from '@ant-design/icons';
import MarkdownEditor from '../MarkdownEditor';
import styles from './ModelSettings.module.less';

const ModelSettings = ({
    modelOptions = [],
    embeddingModelOptions = [],
    modelId,
    embeddingModelId,
    rolePrompt,
    rulePrompt,
    responsePrompt,
    onUpdateModelId,
    onUpdateEmbeddingModelId,
    onUpdateModelSettings
}) => {
    const [dialogVisible, setDialogVisible] = useState(false);
    const [localRolePrompt, setLocalRolePrompt] = useState(rolePrompt);
    const [localRulePrompt, setLocalRulePrompt] = useState(rulePrompt);
    const [localResponsePrompt, setLocalResponsePrompt] = useState(responsePrompt);
    const [functionCallSupported, setFunctionCallSupported] = useState(false);

    const validatedModelOptions = modelOptions.filter((item) => item.id);
    const validatedEmbeddingModelOptions = embeddingModelOptions.filter((item) => item.id);

    useEffect(() => {
        if (!modelId) {
            setFunctionCallSupported(false);
            return;
        }

        const currentModel = modelOptions.find(
            (model) => model.id.toString() === modelId.toString()
        );
        const supported = currentModel && currentModel.functionCall === 1;
        setFunctionCallSupported(supported);
    }, [modelId, modelOptions]);

    useEffect(() => {
        setLocalRulePrompt(rulePrompt);
    }, [rulePrompt]);

    useEffect(() => {
        setLocalResponsePrompt(responsePrompt);
    }, [responsePrompt]);

    useEffect(() => {
        setLocalRolePrompt(rolePrompt);
    }, [rolePrompt]);

    const openPromptDialog = () => {
        setDialogVisible(true);
    };

    const savePrompts = () => {
        if (localRolePrompt.trim() === '') {
            message.error('角色提示不能为空');
            return;
        }

        onUpdateModelSettings({
            rolePrompt: localRolePrompt,
            rulePrompt: localRulePrompt,
            responsePrompt: localResponsePrompt
        });

        message.success('设置保存成功');
        setDialogVisible(false);
    };

    return (
        <div className={styles.modelSettings}>
            <div className={styles.sectionTitle}>
                模型设置
                <Button
                    type="link"
                    icon={<EditOutlined />}
                    className={styles.actionButton}
                    onClick={openPromptDialog}
                >
                    用户指令
                </Button>
            </div>

            <div className={styles.settingsContent}>
                <div className={styles.inputSection}>
                    <div className={styles.sectionLabel}>生成模型</div>
                    <Select
                        value={modelId}
                        placeholder="qianfan-ernie-4.0-8k"
                        className={styles.modelSelector}
                        onChange={onUpdateModelId}
                    >
                        {validatedModelOptions.map((item) => (
                            <Select.Option key={item.id} value={item.id}>
                                {`${item.platform}-${item.name}`}
                            </Select.Option>
                        ))}
                    </Select>
                </div>

                <div className={styles.inputSection}>
                    <div className={styles.sectionLabel}>切词模型</div>
                    <Select
                        value={embeddingModelId}
                        placeholder="选择切词模型"
                        className={styles.modelSelector}
                        onChange={onUpdateEmbeddingModelId}
                        allowClear
                    >
                        {validatedEmbeddingModelOptions.map((item) => (
                            <Select.Option key={item.id} value={item.id}>
                                {`${item.platform}-${item.name}`}
                            </Select.Option>
                        ))}
                    </Select>
                </div>
            </div>

            <Modal
                open={dialogVisible}
                title="模型交互设置"
                width="70%"
                onCancel={() => setDialogVisible(false)}
                footer={[
                    <Button key="cancel" onClick={() => setDialogVisible(false)}>
                        取消
                    </Button>,
                    <Button key="save" type="primary" onClick={savePrompts}>
                        保存
                    </Button>
                ]}
            >
                <div className={styles.modalSection}>
                    <div className={styles.modalLabel}>系统角色</div>
                    <Input
                        value={localRolePrompt}
                        placeholder="设置模型的角色"
                        onChange={(e) => setLocalRolePrompt(e.target.value)}
                    />
                </div>

                <div className={styles.modalSection}>
                    <div className={styles.modalLabel}>用户指令</div>
                    <MarkdownEditor
                        value={localRulePrompt}
                        height="300px"
                        onChange={setLocalRulePrompt}
                    />
                </div>

                <div className={styles.modalSection}>
                    <div className={styles.modalLabel}>应用回复</div>
                    <MarkdownEditor
                        value={localResponsePrompt}
                        height="300px"
                        onChange={setLocalResponsePrompt}
                    />
                </div>
            </Modal>
        </div>
    );
};

export default ModelSettings;
