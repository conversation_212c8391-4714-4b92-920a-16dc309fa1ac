import React, { useState, useEffect } from 'react';
import { Input } from 'antd';

const { TextArea } = Input;

// 简易版
const MarkdownEditor = ({ value, height = '300px', onChange }) => {
    const [localValue, setLocalValue] = useState(value || '');

    useEffect(() => {
        setLocalValue(value || '');
    }, [value]);

    const handleChange = (e) => {
        const newValue = e.target.value;
        setLocalValue(newValue);
        onChange && onChange(newValue);
    };

    return (
        <TextArea
            value={localValue}
            onChange={handleChange}
            style={{ height }}
            placeholder="请输入内容..."
            autoSize={{ minRows: 6, maxRows: 12 }}
        />
    );
};

export default MarkdownEditor;
