import React, { useState, useEffect } from 'react';
import { Modal, Table } from 'antd';
import { getKDB } from 'COMMON/api/qe_rag/book';

const AgentTable = ({
    visible,
    onClose,
    groupId,
    embeddingModelId,
    onUpdateValue,
    selectedKnowledgeBases = [] // 添加已选择的知识库参数
}) => {
    const [selectedRows, setSelectedRows] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [allTableData, setAllTableData] = useState([]); // 存储所有数据，用于过滤

    useEffect(() => {
        if (groupId && groupId !== 0) {
            getTableData();
        }
    }, [groupId]);

    useEffect(() => {
        if (allTableData && allTableData.length > 0) {
            setTableData(filterTableData(allTableData));
        }
    }, [embeddingModelId, allTableData]);

    // 初始化已选择的知识库
    useEffect(() => {
        if (visible && selectedKnowledgeBases && selectedKnowledgeBases.length > 0) {
            setSelectedRows(selectedKnowledgeBases);
        } else if (visible) {
            setSelectedRows([]);
        }
    }, [visible, selectedKnowledgeBases]);

    const getTableData = async () => {
        if (!groupId || groupId === 0) {
            return;
        }

        const res = await getKDB(groupId);

        setAllTableData(res ?? []);
        setTableData(filterTableData(res) ?? []);
    };

    const filterTableData = (data) => {
        if (!data || data.length === 0) {
            return [];
        }
        if (!embeddingModelId) {
            return data;
        } else {
            // 根据切词模型ID过滤知识库
            const embeddingModelIdNum =
                typeof embeddingModelId === 'string'
                    ? parseInt(embeddingModelId, 10)
                    : embeddingModelId;

            return data.filter((item) => item.embeddingModelId === embeddingModelIdNum);
        }
    };

    const handleSelectionChange = (selectedRowKeys, selectedRows) => {
        const validSelectedRows = selectedRows ? selectedRows.filter((row) => row && row.id) : [];
        setSelectedRows(validSelectedRows);
        onUpdateValue(validSelectedRows);
    };

    const columns = [
        {
            title: '名字',
            dataIndex: 'name',
            key: 'name',
            width: 300
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            width: 400
        }
    ];

    const rowSelection = {
        type: 'checkbox',
        selectedRowKeys: selectedRows.filter((row) => row && row.id).map((row) => row.id),
        onChange: handleSelectionChange
    };

    return (
        <Modal open={visible} title="知识库关联" onCancel={onClose} footer={null} width={800}>
            <Table
                rowSelection={rowSelection}
                dataSource={tableData}
                columns={columns}
                rowKey="id"
                pagination={false}
                scroll={{ y: 400 }}
            />
        </Modal>
    );
};

export default AgentTable;
