import { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { Input, Button, Avatar, message, Select } from 'antd';
import { UserOutlined, SendOutlined } from '@ant-design/icons';
import { parse as marked } from 'marked';
import debounce from 'lodash/debounce';
import qeRagIcon from 'RESOURCES/img/qeRagIcon.png';
import styles from './AgentPreview.module.less';

const { Option } = Select;

// 聊天消息组件
const ChatMessage = ({ isUser, content, isLoading = false }) => {
    return (
        <div
            className={`${styles.messageWrapper} ${
                isUser ? styles.userMessageWrapper : styles.aiMessageWrapper
            }`}
        >
            {!isUser && (
                <div className={styles.messageAvatar}>
                    <div className={styles.aiAvatar}>🤖</div>
                </div>
            )}
            <div className={styles.messageContent}>
                <div
                    className={`${styles.messageBubble} ${
                        isUser ? styles.userMessage : styles.aiMessage
                    }`}
                >
                    {isLoading ? (
                        <div className={styles.loadingContainer}>
                            <div className={styles.loadingDots}>
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                            <span className={styles.loadingText}>AI 正在思考中...</span>
                        </div>
                    ) : (
                        <div dangerouslySetInnerHTML={{ __html: content }} /> // bca-disable-line
                    )}
                </div>
            </div>
            {isUser && (
                <div className={styles.messageAvatar}>
                    <Avatar
                        size={32}
                        icon={<UserOutlined />}
                        style={{ backgroundColor: '#1677ff' }}
                    />
                </div>
            )}
        </div>
    );
};

// 欢迎页面组件
const WelcomePage = () => {
    return (
        <div className={styles.welcomeContainer}>
            <div className={styles.welcomeContent}>
                <div className={styles.welcomeIcon}>
                    <img src={qeRagIcon} style={{ width: '50px', height: '50px' }} alt="QE-RAG" />
                </div>
                <h1 className={styles.welcomeTitle}>我是 QE-RAG 很高兴见到你！</h1>
                {/* <p className={styles.welcomeSubtitle}>请把你的任务交给我吧~</p> */}
            </div>
        </div>
    );
};

// 聊天容器组件
const ChatContainer = ({ conversations, currentQuestion, currentAnswer, isLoading }) => {
    const containerRef = useRef(null);
    const collapsedStateRef = useRef({ docs: true, think: true });

    // 自动滚动到底部
    const scrollToBottom = useCallback(() => {
        if (containerRef.current) {
            containerRef.current.scrollTop = containerRef.current.scrollHeight;
        }
    }, []);

    useEffect(() => {
        scrollToBottom();
    }, [conversations, currentAnswer, scrollToBottom]);

    // 添加折叠面板的点击事件监听
    useEffect(() => {
        const handleCollapsibleClick = (event) => {
            const target = event.target;

            // 检查是否点击了参考文档的标题
            if (target.closest('.reference-docs-header')) {
                const header = target.closest('.reference-docs-header');
                const content = header.nextElementSibling;
                const arrow = header.querySelector('.custom-arrow');

                if (content && arrow) {
                    // 切换折叠状态
                    collapsedStateRef.current.docs = !collapsedStateRef.current.docs;
                    const isCollapsed = collapsedStateRef.current.docs;

                    // 更新显示状态
                    content.style.display = isCollapsed ? 'none' : 'flex';

                    // 更新箭头方向
                    arrow.classList.remove('arrow-down', 'arrow-right');
                    arrow.classList.add(isCollapsed ? 'arrow-right' : 'arrow-down');
                }
                event.preventDefault();
                event.stopPropagation();
                return;
            }

            // 检查是否点击了思考过程的标题
            if (target.closest('.think-process-header')) {
                const header = target.closest('.think-process-header');
                const content = header.nextElementSibling;
                const arrow = header.querySelector('.custom-arrow');

                if (content && arrow) {
                    // 切换折叠状态
                    collapsedStateRef.current.think = !collapsedStateRef.current.think;
                    const isCollapsed = collapsedStateRef.current.think;

                    // 更新显示状态
                    content.style.display = isCollapsed ? 'none' : 'flex';

                    // 更新箭头方向
                    arrow.classList.remove('arrow-down', 'arrow-right');
                    arrow.classList.add(isCollapsed ? 'arrow-right' : 'arrow-down');
                }
                event.preventDefault();
                event.stopPropagation();
                return;
            }
        };

        const container = containerRef.current;
        if (container) {
            container.addEventListener('click', handleCollapsibleClick);
            return () => {
                container.removeEventListener('click', handleCollapsibleClick);
            };
        }
    }, []);

    const isEmpty = conversations.length === 0 && !currentQuestion;

    return (
        <div ref={containerRef} className={styles.chatContainer}>
            {isEmpty ? (
                <WelcomePage />
            ) : (
                <>
                    {conversations.map((conv) => (
                        <div key={conv.id} className={styles.conversationItem}>
                            <ChatMessage isUser content={conv.question} />
                            <ChatMessage isUser={false} content={conv.answer} />
                        </div>
                    ))}

                    {currentQuestion && (
                        <div className={styles.conversationItem}>
                            <ChatMessage isUser content={currentQuestion} />
                            {currentAnswer ? (
                                <ChatMessage isUser={false} content={currentAnswer} />
                            ) : isLoading ? (
                                <ChatMessage isUser={false} content="" isLoading />
                            ) : null}
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

const AgentPreview = ({ agentId }) => {
    const [prompt, setPrompt] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [currentQuestion, setCurrentQuestion] = useState('');
    const [currentAnswer, setCurrentAnswer] = useState('');
    const [conversations, setConversations] = useState([]);
    const [rawBuffer, setRawBuffer] = useState('');
    const [thinkBuffer, setThinkBuffer] = useState('');
    const [docLinks, setDocLinks] = useState([]);

    // 渲染Markdown内容的辅助函数
    const renderMarkdown = async (content) => {
        try {
            return marked(content);
        } catch (error) {
            console.error('Markdown rendering error:', error);
            return content.replace(/\n/g, '<br>');
        }
    };

    // 缓存已渲染的部分，避免重复渲染
    const renderedPartsRef = useRef({
        docs: '',
        think: '',
        content: ''
    });

    // 缓存折叠状态
    const collapsedStateRef = useRef({
        docs: false,
        think: false
    });

    // 更新文档显示 - 优化为增量更新
    const updateDocumentsDisplay = useCallback(async () => {
        try {
            let finalContent = '';
            let hasChanges = false;

            // 处理参考文档部分
            const currentDocsKey = JSON.stringify(docLinks);
            if (docLinks.length > 0) {
                if (renderedPartsRef.current.docsKey !== currentDocsKey) {
                    const isDocsCollapsed = collapsedStateRef.current.docs;
                    const docsHtml = `
                        <div class="reference-docs-container">
                            <div class="reference-docs-header">
                                <span class="reference-docs-title">参考文档 <span class="custom-arrow ${
                                    isDocsCollapsed ? 'arrow-right' : 'arrow-down'
                                }"></span></span>
                            </div>
                            <div class="reference-docs-content" style="display: ${
                                isDocsCollapsed ? 'none' : 'flex'
                            };">
                                ${docLinks
                                    .map(
                                        (doc, index) => `
                                    <div class="doc-link-item">
                                        <span>文档 ${index + 1}： </span>
                                        <a href="${
                                            window.location.origin
                                        }/#/qe_rag/knowledge/docs?=knowledgeId=${doc.id}&docId=${
                                            doc.docId
                                        }" target="_blank">
                                            ${doc.fileName}
                                        </a>
                                    </div>
                                `
                                    )
                                    .join('')}
                            </div>
                        </div>
                    `;
                    renderedPartsRef.current.docs = docsHtml;
                    renderedPartsRef.current.docsKey = currentDocsKey;
                    hasChanges = true;
                }
                finalContent += renderedPartsRef.current.docs;
            }

            // 处理思考过程部分
            if (thinkBuffer.trim()) {
                if (renderedPartsRef.current.thinkBuffer !== thinkBuffer) {
                    const isThinkCollapsed = collapsedStateRef.current.think;
                    const thinkHtml = `
                        <div class="think-process-container">
                            <div class="think-process-header">
                                <span class="think-process-title">思考和行动过程 <span class="custom-arrow ${
                                    isThinkCollapsed ? 'arrow-right' : 'arrow-down'
                                }"></span></span>
                            </div>
                            <div class="think-process-content" style="display: ${
                                isThinkCollapsed ? 'none' : 'flex'
                            };">
                                ${await renderMarkdown(
                                    thinkBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n')
                                )}
                            </div>
                        </div>
                    `;
                    renderedPartsRef.current.think = thinkHtml;
                    renderedPartsRef.current.thinkBuffer = thinkBuffer;
                    hasChanges = true;
                }
                finalContent += renderedPartsRef.current.think;
            }

            // 处理主要内容部分
            if (rawBuffer) {
                if (renderedPartsRef.current.rawBuffer !== rawBuffer) {
                    const renderedContent = await renderMarkdown(
                        rawBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n')
                    );
                    renderedPartsRef.current.content = renderedContent;
                    renderedPartsRef.current.rawBuffer = rawBuffer;
                    hasChanges = true;
                }
                finalContent += renderedPartsRef.current.content;
            }

            // 只有在有变化且有实际内容时才更新
            if (hasChanges && finalContent.trim()) {
                setCurrentAnswer(finalContent);
            } else if (!finalContent.trim() && !rawBuffer && !thinkBuffer && !docLinks.length) {
                // 只有在所有内容都为空时才清空（比如新对话开始时）
                setCurrentAnswer('');
            }
        } catch (error) {
            console.error('更新文档显示时出错:', error);
        }
    }, [rawBuffer, docLinks, thinkBuffer, renderMarkdown]);

    // 防抖更新显示内容
    const debouncedUpdateDocumentsDisplay = useMemo(
        () => debounce(updateDocumentsDisplay, 50), // 减少延迟，因为现在有缓存
        [updateDocumentsDisplay]
    );

    // 实时更新显示内容
    useEffect(() => {
        if (rawBuffer || docLinks.length > 0 || thinkBuffer) {
            debouncedUpdateDocumentsDisplay();
        }
    }, [rawBuffer, docLinks, thinkBuffer, debouncedUpdateDocumentsDisplay]);

    // 处理提交逻辑
    const handleSubmit = async () => {
        if (!agentId || !prompt.trim()) {
            message.error('请先保存Agent配置并输入问题');
            return;
        }

        const currentPrompt = prompt;
        setIsLoading(true);
        setCurrentQuestion(currentPrompt);
        setCurrentAnswer('');
        setPrompt('');
        setDocLinks([]);
        setRawBuffer('');
        setThinkBuffer('');

        try {
            const response = await fetch('/rag/api/agent/stream?stream=true', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    Connection: 'keep-alive'
                },
                body: JSON.stringify({
                    query: currentPrompt,
                    agentId: agentId
                })
            });

            if (!response.ok) {
                throw new Error('请求失败');
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder('utf-8');

            if (reader) {
                let tempBuffer = '';
                let localRawBuffer = '';
                let localThinkBuffer = '';
                let localDocLinks = [];

                while (true) {
                    const { value, done } = await reader.read();

                    if (done) {
                        break;
                    }

                    const chunk = decoder.decode(value, { stream: true });
                    tempBuffer += chunk;
                    const lines = tempBuffer.split('\n');
                    tempBuffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.startsWith(':')) {
                            continue;
                        }
                        if (line.startsWith('data:"')) {
                            const content = line.substring(6, line.length - 1);

                            // 处理特殊的docurl前缀
                            if (content.startsWith('docurl:')) {
                                const docInfo = content.substring(7);
                                const parts = docInfo.split('-');
                                if (parts.length >= 3) {
                                    const id = parts[0];
                                    const docId = parts[1];
                                    const fileName = parts
                                        .slice(2)
                                        .join('-')
                                        .replace(/\\n/g, '')
                                        .trim();
                                    localDocLinks.push({ id, fileName, docId });
                                    setDocLinks([...localDocLinks]);
                                }
                                continue;
                            }

                            // 处理思考过程
                            if (content.startsWith('think:')) {
                                const thinkContent = content.substring(6);
                                localThinkBuffer += thinkContent;
                                setThinkBuffer(localThinkBuffer);
                                continue;
                            }

                            // 正常内容直接添加
                            localRawBuffer += content;
                            setRawBuffer(localRawBuffer);
                        }
                    }
                }

                // 处理最后一行数据
                if (tempBuffer.startsWith('data:"')) {
                    const content = tempBuffer.substring(6, tempBuffer.length - 1);
                    if (content.startsWith('docurl:')) {
                        const docInfo = content.substring(7);
                        const parts = docInfo.split('-');
                        if (parts.length >= 3) {
                            const id = parts[0];
                            const docId = parts[1];
                            const fileName = parts.slice(2).join('-').replace(/\\n/g, '').trim();
                            localDocLinks.push({ id, fileName, docId });
                            setDocLinks([...localDocLinks]);
                        }
                    } else if (content.startsWith('think:')) {
                        const thinkContent = content.substring(6);
                        localThinkBuffer += thinkContent;
                        setThinkBuffer(localThinkBuffer);
                    } else {
                        localRawBuffer += content;
                        setRawBuffer(localRawBuffer);
                    }
                }

                // 等待一小段时间确保所有状态都已更新
                setTimeout(async () => {
                    // 获取最终的答案内容
                    let finalAnswer = '';
                    let renderedContent = await renderMarkdown(
                        localRawBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n')
                    );

                    // 构建完整的答案内容
                    if (localDocLinks.length > 0) {
                        const docsHtml = `
                            <div class="reference-docs-container">
                                <div class="reference-docs-header">
                                    <span class="reference-docs-title">
                                    参考文档 <span class="custom-arrow arrow-down"></span>
                                    </span>
                                </div>
                                <div class="reference-docs-content">
                                    ${localDocLinks
                                        .map(
                                            (doc, index) => `
                                        <div class="doc-link-item">
                                            <span>文档 ${index + 1}： </span>
                                            <a href="${
                                                window.location.origin
                                            }/#/qe_rag/knowledge/docs?knowledgeId=${doc.id}&docId=${
                                                doc.docId
                                            }" target="_blank">
                                                ${doc.fileName}
                                            </a>
                                        </div>
                                    `
                                        )
                                        .join('')}
                                </div>
                            </div>
                        `;
                        finalAnswer += docsHtml;
                    }

                    if (localThinkBuffer.trim()) {
                        const thinkHtml = `
                            <div class="think-process-container">
                                <div class="think-process-header">
                                    <span class="think-process-title">
                                        思考和行动过程 
                                    <span class="custom-arrow arrow-down">
                                    </span></span>
                                </div>
                                <div class="think-process-content" style="display: flex;">
                                    ${await renderMarkdown(
                                        localThinkBuffer.replace(/\\"/g, '"').replace(/\\n/g, '\n')
                                    )}
                                </div>
                            </div>
                        `;
                        finalAnswer += thinkHtml;
                    }

                    finalAnswer += renderedContent;

                    setConversations((prev) => [
                        ...prev,
                        {
                            id: Date.now() + Math.random(),
                            question: currentPrompt,
                            answer: finalAnswer
                        }
                    ]);

                    // 清空当前问题和答案
                    setCurrentQuestion('');
                    setCurrentAnswer('');
                    setDocLinks([]);
                    setRawBuffer('');
                    setThinkBuffer('');
                }, 200);
            }
        } catch (error) {
            console.error('Error:', error);
            setCurrentAnswer('请求失败，请重试');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className={styles.container}>
            <ChatContainer
                conversations={conversations}
                currentQuestion={currentQuestion}
                currentAnswer={currentAnswer}
                isLoading={isLoading}
            />

            <div className={styles.inputContainer}>
                <div className={styles.inputWrapper}>
                    <Input
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        placeholder="输入你的问题，我来帮你解答..."
                        onPressEnter={handleSubmit}
                        disabled={isLoading}
                        style={{
                            flex: 1,
                            border: 'none',
                            boxShadow: 'none',
                            fontSize: '15px',
                            backgroundColor: 'transparent'
                        }}
                        size="large"
                    />

                    <Button
                        type="primary"
                        icon={<SendOutlined />}
                        onClick={handleSubmit}
                        loading={isLoading}
                        disabled={!prompt.trim() || !agentId || isLoading}
                        className={styles.sendButton}
                    />
                </div>
            </div>
        </div>
    );
};

export default AgentPreview;
