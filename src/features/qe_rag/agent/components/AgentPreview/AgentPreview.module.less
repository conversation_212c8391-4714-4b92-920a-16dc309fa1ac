.container {
    height: 100%;
    display: flex;
    flex-direction: column;
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.chatContainer {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;

        &:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    }
}

.conversationItem {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.messageWrapper {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    max-width: 85%;

    &.userMessageWrapper {
        align-self: flex-end;
        flex-direction: row-reverse !important;
        justify-content: flex-start;
    }

    &.aiMessageWrapper {
        align-self: flex-start;
        flex-direction: row;
    }
}

.messageAvatar {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.aiAvatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
}

.messageContent {
    flex: 1;
    min-width: 0;
}

.messageBubble {
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.5;

    &.userMessage {
        background: #1677ff;
        color: white;
        border-bottom-right-radius: 6px;
    }

    &.aiMessage {
        background: rgba(255, 255, 255, 0.95);
        color: #333;
        border-bottom-left-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

.loadingContainer {
    display: flex;
    align-items: center;
    gap: 8px;
}

.loadingDots {
    display: flex;
    gap: 4px;

    span {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #1677ff;
        animation: loadingDot 1.4s infinite ease-in-out both;

        &:nth-child(1) {
            animation-delay: -0.32s;
        }

        &:nth-child(2) {
            animation-delay: -0.16s;
        }

        &:nth-child(3) {
            animation-delay: 0s;
        }
    }
}

@keyframes loadingDot {

    0%,
    80%,
    100% {
        transform: scale(0);
        opacity: 0.5;
    }

    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.loadingText {
    font-size: 14px;
    color: #666;
}

.welcomeContainer {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.welcomeContent {
    color: white;

    .welcomeIcon {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }

    .welcomeTitle {
        font-size: 28px;
        font-weight: 600;
        margin: 0 0 12px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .welcomeSubtitle {
        font-size: 16px;
        opacity: 0.8;
        margin: 0;
    }
}

.inputContainer {
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.inputWrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    background: white;
    border-radius: 24px;
    padding: 8px 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e8e8e8;

    &:focus-within {
        border-color: #1677ff;
        box-shadow: 0 4px 20px rgba(22, 119, 255, 0.2);
    }
}

.sendButton {
    border-radius: 50%;
    width: 40px !important;
    height: 40px !important;
    display: flex;
    flex-shrink: 1;
    align-items: center;
    justify-content: center;
    border: none;
    background: #1677ff;
    color: white;
    cursor: pointer;
    transition: all 0.2s;

    &:hover:not(:disabled) {
        background: #4096ff;
        transform: scale(1.05);
    }

    &:disabled {
        background: #d9d9d9;
        cursor: not-allowed;
        transform: none;
    }
}

/* 参考文档和思考过程样式 */
:global(.reference-docs-container),
:global(.think-process-container) {
    margin: 16px 0;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
    background: #fafafa;
}

:global(.reference-docs-header),
:global(.think-process-header) {
    padding: 12px 16px;
    background: #f0f0f0;
    border-bottom: 1px solid #e8e8e8;
    cursor: pointer;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    &:hover {
        background: #e8e8e8;
    }
}

:global(.reference-docs-title),
:global(.think-process-title) {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

:global(.custom-arrow) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 12px;
    height: 12px;
    transition: transform 0.2s ease;

    &.arrow-down {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 6px solid #666;
    }

    &.arrow-right {
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        border-left: 6px solid #666;
    }
}

:global(.reference-docs-content),
:global(.think-process-content) {
    padding: 16px;
    background: white;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

:global(.doc-link-item) {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }

    span {
        color: #666;
        font-size: 14px;
        flex-shrink: 0;
    }

    a {
        color: #1677ff;
        text-decoration: none;
        font-size: 14px;

        &:hover {
            text-decoration: underline;
        }
    }
}

:global(.think-process-content) {
    font-size: 14px;
    line-height: 1.6;
    color: #333;

    p {
        margin: 8px 0;

        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    code {
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 13px;
    }

    pre {
        background: #f5f5f5;
        padding: 12px;
        border-radius: 6px;
        overflow-x: auto;
        margin: 12px 0;

        code {
            background: none;
            padding: 0;
        }
    }

    ul,
    ol {
        padding-left: 20px;
        margin: 8px 0;
    }

    li {
        margin: 4px 0;
    }

    blockquote {
        border-left: 4px solid #1677ff;
        padding-left: 16px;
        margin: 12px 0;
        color: #666;
        font-style: italic;
    }
}