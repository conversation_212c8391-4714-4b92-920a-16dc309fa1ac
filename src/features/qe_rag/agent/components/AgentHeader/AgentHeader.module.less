.upperRow {
    margin-bottom: 10px;
}

.headerContent {
    background-color: white;
    margin-top: 5px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(96, 92, 229, 0.08);
    border: 1px solid rgba(96, 92, 229, 0.1);
}

.backButton {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px;
}

.backBtn {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #605ce5;
    font-size: 14px;
    font-weight: 500;
    padding: 6px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;

    &:hover {
        background: rgba(96, 92, 229, 0.08);
        color: #4c46d6;
    }

    :global(.anticon) {
        font-size: 14px;
    }
}

.headerTitle {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
}

.headerActions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px;
    gap: 12px;
}

.publishBtn {
    height: 36px;
    padding: 0 20px;
    border-radius: 8px;
    font-weight: 500;
    background: #605ce5;
    border-color: #605ce5;
    box-shadow: 0 2px 8px rgba(96, 92, 229, 0.2);

    &:hover {
        background: #4c46d6;
        border-color: #4c46d6;
        box-shadow: 0 4px 12px rgba(96, 92, 229, 0.3);
    }
}
