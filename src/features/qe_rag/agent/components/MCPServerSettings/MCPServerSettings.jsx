import React, { useState, useEffect } from 'react';
import { Button, Table, Tooltip } from 'antd';
import { EditOutlined, DeleteOutlined, ToolOutlined } from '@ant-design/icons';
import styles from './MCPServerSettings.module.less';

const MCPServerSettings = ({
    selectedMCPServers = [],
    functionCallSupported = false,
    onUpdateSelectedMCPServers,
    onOpenMCPServerDialog
}) => {
    const removeMCPServer = (index) => {
        const updatedServers = [...selectedMCPServers];
        updatedServers.splice(index, 1);
        onUpdateSelectedMCPServers(updatedServers);
    };

    const columns = [
        {
            title: '',
            dataIndex: 'order',
            key: 'order',
            width: 40,
            render: (_, record, index) => <div className={styles.orderNumber}>{index + 1}</div>
        },
        {
            title: 'Server 名称',
            dataIndex: 'mcpServerName',
            key: 'mcpServerName',
            width: 120
        },
        {
            title: '功能描述',
            dataIndex: 'description',
            key: 'description'
        },
        {
            title: '',
            key: 'action',
            width: 80,
            render: (_, record, index) => (
                <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => removeMCPServer(index)}
                />
            )
        }
    ];

    return (
        <div className={styles.mcpserverSettings}>
            <div className={styles.sectionTitle}>
                MCP Server
                <Tooltip
                    title={functionCallSupported ? '' : '当前模型不支持关联MCP Server'}
                    placement="top"
                >
                    <Button
                        type="link"
                        icon={<EditOutlined />}
                        className={styles.actionButton}
                        onClick={onOpenMCPServerDialog}
                        disabled={!functionCallSupported}
                    >
                        关联 MCP Server
                    </Button>
                </Tooltip>
            </div>

            <div className={styles.settingsContent}>
                <div className={styles.tableSection}>
                    <Table
                        dataSource={selectedMCPServers}
                        columns={columns}
                        locale={{ emptyText: '暂无关联 MCP Server' }}
                        className={styles.mcpserverTable}
                        pagination={false}
                        size="small"
                        rowKey={(record, index) => `${record.mcpServerId || record.id}-${index}`}
                    />
                </div>
            </div>
        </div>
    );
};

export default MCPServerSettings;
