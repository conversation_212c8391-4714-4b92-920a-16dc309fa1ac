.mcpserverSettings {
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    
    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        border-color: #d1d9e0;
    }
}

.sectionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid #f0f2f5;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    &::before {
        content: '🔧';
        font-size: 16px;
        margin-right: 8px;
    }
}

.sectionIcon {
    margin-right: 8px;
    color: #8c8c8c;
}

.actionButton {
    font-size: 13px;
    color: #1890ff;
    padding: 0;
    height: auto;
    
    &:hover {
        color: #40a9ff;
    }
    
    &:disabled {
        color: #bfbfbf;
    }
}

.settingsContent {
    padding: 24px;
}

.tableSection {
    margin-top: 0;
}

.mcpserverTable {
    border: 1px solid #e8e8e8;
    border-radius: 6px;

    :global(.ant-table-cell) {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    :global(.ant-table-thead > tr > th) {
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        font-weight: 600;
    }
    
    :global(.ant-table-tbody > tr:hover > td) {
        background: #f5f5f5;
    }
}

.orderNumber {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: #1890ff;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}
