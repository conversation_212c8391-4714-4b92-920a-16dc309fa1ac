// 编辑页面样式 - 与知识库页面风格统一
.editContainer {
    height: 100vh;
    background: #f3f2fe87;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

// 列表页面样式
.container {
    height: 100vh;
    background: #f3f2fe87;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 固定头部区域 */
.fixedHeader {
    flex-shrink: 0;
    padding: 24px 24px 0 24px;
    background: #f3f2fe53;
    backdrop-filter: blur(10px);
}

/* 可滚动内容区域 */
.scrollableContent {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background: #f5f5f5;
    position: relative;
    padding: 24px;

    /* Loading 状态样式 */
    :global(.ant-spin-container) {
        min-height: 400px;
    }

    :global(.ant-spin-spinning) {
        :global(.ant-spin-container) {
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

/* 搜索栏 */
.searchBar {
    margin-top: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.searchInput {
    max-width: 320px;
    height: 36px;
    border-radius: 12px;
    border: 1px solid rgba(96, 92, 229, 0.2);
    background: #ffffff;

    :global(.ant-input) {
        border: none;
        box-shadow: none;
        font-size: 14px;
        color: #333;
        background: transparent;
    }
}

.headerRight {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* 卡片网格 */
.cardGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

/* 智能体卡片 */
.agentCard {
    border-radius: 16px;
    border: 1px solid rgba(96, 92, 229, 0.15);
    background: #ffffff;
    box-shadow: 0 4px 16px rgba(96, 92, 229, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;

    &:hover {
        box-shadow: 0 12px 32px rgba(96, 92, 229, 0.15);
        transform: translateY(-3px);
        border-color: rgba(96, 92, 229, 0.25);
    }

    :global(.ant-card-body) {
        padding: 0;
    }
}

.cardContent {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
}

.cardHeader {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 12px;
    position: relative;
    flex: 1;
}

.cardCover {
    width: 88px;
    height: 88px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 12px;
    }
}

.cardTitle {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
}

.agentName {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cardId {
    font-size: 10px;
    color: #605ce5;
    font-weight: 500;
    background: rgba(96, 92, 229, 0.1);
    padding: 2px 6px;
    border-radius: 6px;
    display: inline-block;
    width: fit-content;
    align-self: flex-start;
}

.agentDesc {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: #666;
    line-height: 1.3;
    min-height: 32px;
}

.cardFooter {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: auto;
    padding-top: 12px;
}

.tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.tag {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #666;
    background: #f5f7fa;
    font-weight: 500;
}

.groupId {
    font-weight: 500;
    margin-right: 4px;
    color: #666;
}

.createTime {
    font-size: 11px;
    color: #999;
    font-weight: 400;
}

.emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;

    .emptyIcon {
        font-size: 64px;
        color: rgba(96, 92, 229, 0.3);
        margin-bottom: 16px;
    }

    .emptyText {
        font-size: 16px;
        color: #666;
        margin-bottom: 24px;
    }
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 32px;

    :global(.ant-pagination) {
        .ant-pagination-item {
            border-color: rgba(96, 92, 229, 0.2);
            border-radius: 8px;

            &:hover {
                border-color: #605ce5;
                color: #605ce5;
            }

            &.ant-pagination-item-active {
                background: #605ce5;
                border-color: #605ce5;
                color: #ffffff;
            }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
            border-color: rgba(96, 92, 229, 0.2);
            border-radius: 8px;

            &:hover {
                border-color: #605ce5;
                color: #605ce5;
            }
        }

        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
            &:hover {
                color: #605ce5;
            }
        }
    }
}

.iconText {
    color: #ffffff;
    font-size: 34px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .fixedHeader {
        padding: 16px 16px 0 16px;
    }

    .scrollableContent {
        padding: 16px;
    }

    .cardGrid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .searchBar {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .searchInput {
        max-width: 100%;
    }
}

.cardContainer {
    display: flex;
    flex: 1;
    min-height: 0;
    gap: 10px;
    overflow: hidden;
    max-height: calc(100vh - 130px);
}

.wideCard {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :global(.ant-card-body) {
        padding: 0px;
        overflow: auto;
        flex: 1;
    }
}

.narrowCard {
    flex: 2;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    :global(.ant-card-body) {
        padding: 0px;
        overflow: auto;
        flex: 1;
    }
}

.sectionHeader {
    font: 14px 'PingFang SC';
    display: inline-flex;
    align-items: center;
    gap: 8px;
}