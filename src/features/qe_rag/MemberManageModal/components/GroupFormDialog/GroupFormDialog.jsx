import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import styles from '../common.module.less';

const { Option } = Select;

const GroupFormDialog = ({ visible, onCancel, formData, isEdit, onSubmitSuccess }) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (visible && formData) {
            form.setFieldsValue({
                name: formData.name,
                business: formData.business,
                manager: formData.manager,
                adminArray: formData.adminArray || [],
                memberArray: formData.memberArray || []
            });
        }
    }, [visible, formData, form]);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const submitData = {
                ...values,
                createUser: formData?.createUser || '',
                admin: values.adminArray?.join(';') || '',
                member: values.memberArray?.join(';') || ''
            };

            if (isEdit) {
                submitData.id = formData.id;
                // await GroupApi.editGroup(submitData);
            } else {
                // await GroupApi.createGroup(submitData);
            }

            message.success(isEdit ? '编辑成功' : '创建成功');
            onSubmitSuccess();
        } catch (error) {
            if (error.errorFields) {
                // 表单验证错误
                return;
            }
            message.error(isEdit ? '编辑失败' : '创建失败');
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <Modal
            title={isEdit ? '编辑群组' : '新增群组'}
            open={visible}
            onOk={handleSubmit}
            onCancel={handleCancel}
            confirmLoading={loading}
            width={600}
        >
            <Form form={form} layout="vertical" preserve={false}>
                <Form.Item
                    label="工作组名称"
                    name="name"
                    rules={[{ required: true, message: '请输入工作组名称' }]}
                >
                    <Input placeholder="请输入工作组名称" />
                </Form.Item>

                <Form.Item
                    label="事业群"
                    name="business"
                    rules={[{ required: true, message: '请输入事业群' }]}
                >
                    <Input placeholder="请输入事业群" />
                </Form.Item>

                <Form.Item
                    label="经理"
                    name="manager"
                    rules={[{ required: true, message: '请输入经理' }]}
                >
                    <Input placeholder="请输入经理" />
                </Form.Item>

                <Form.Item label="管理员" name="adminArray">
                    <Select
                        mode="tags"
                        placeholder="请选择或输入管理员"
                        style={{ width: '100%' }}
                    />
                </Form.Item>

                <Form.Item label="成员" name="memberArray">
                    <Select mode="tags" placeholder="请选择或输入成员" style={{ width: '100%' }}>
                        {/* TODO：成员列表组件 */}
                    </Select>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default GroupFormDialog;
