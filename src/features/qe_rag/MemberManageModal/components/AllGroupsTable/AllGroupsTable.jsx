import { useState, useEffect } from 'react';
import { Table, Tag, Button, Card, Form, Input, Pagination, message } from 'antd';
import GroupFormDialog from '../GroupFormDialog';
import ApplyGroupDialog from '../ApplyGroupDialog';
import { formatDate, splitMembers } from '../../../../pages/qe_rag/utils';
import styles from '../common.module.less';

const AllGroupsTable = () => {
    const [tableData, setTableData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [dialogVisible, setDialogVisible] = useState(false);
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentFormData, setCurrentFormData] = useState(null);
    const [applyDialogVisible, setApplyDialogVisible] = useState(false);
    const [currentGroupId, setCurrentGroupId] = useState(0);
    const [currentGroupName, setCurrentGroupName] = useState('');
    const [currentAdmins, setCurrentAdmins] = useState([]);
    const [filterForm] = Form.useForm();
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });

    const getInitialFormData = () => ({
        id: 0,
        name: '',
        createUser: '', // 从用户store获取
        business: '',
        manager: '',
        member: '',
        admin: '',
        managerArray: [],
        memberArray: [],
        adminArray: [],
        createAt: '',
        updateAt: ''
    });

    const getTableData = async (params = {}) => {
        setLoading(true);
        try {
            const filterValues = filterForm.getFieldsValue();
            const queryParams = {
                ...filterValues,
                page: pagination.current,
                size: pagination.pageSize,
                ...params
            };
            // 调用API获取所有组数据
            // const res = await GroupApi.getGroups(queryParams);
            // setTableData(res.data.items || []);
            // setPagination(prev => ({ ...prev, total: res.data.total || 0 }));

            // 模拟数据
            setTableData([]);
        } catch (error) {
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    };

    const handleFilter = () => {
        setPagination((prev) => ({ ...prev, current: 1 }));
        getTableData();
        message.success('筛选条件已应用');
    };

    const handleSizeChange = (current, size) => {
        setPagination((prev) => ({ ...prev, pageSize: size, current: 1 }));
        getTableData();
    };

    const handleCurrentChange = (page) => {
        setPagination((prev) => ({ ...prev, current: page }));
        getTableData();
    };

    const canEdit = (row) => {
        const username = ''; // 从用户store获取
        return row.admin?.split(';').includes(username);
    };

    const handleCreate = () => {
        setIsEditMode(false);
        setCurrentFormData(getInitialFormData());
        setDialogVisible(true);
    };

    const handleEdit = (row) => {
        if (!canEdit(row)) {
            message.warning('您没有权限编辑该群组');
            return;
        }
        setIsEditMode(true);
        setCurrentFormData({
            ...row,
            managerArray: row.manager,
            memberArray: row.member?.split(';').filter(Boolean) || [],
            adminArray: row.admin?.split(';').filter(Boolean) || []
        });
        setDialogVisible(true);
    };

    const handleApply = (row) => {
        setCurrentGroupId(row.id);
        setCurrentGroupName(row.name);
        setCurrentAdmins(splitMembers(row.admin));
        setApplyDialogVisible(true);
    };

    const columns = [
        {
            title: '工作组名称',
            dataIndex: 'name',
            key: 'name',
            ellipsis: true
        },
        {
            title: '管理员',
            dataIndex: 'admin',
            key: 'admin',
            width: 200,
            ellipsis: true,
            render: (admin) => (
                <div className={styles.memberTags}>
                    {splitMembers(admin).map((member) => (
                        <Tag key={member} size="small" className={styles.memberTag}>
                            {member}
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: '成员',
            dataIndex: 'member',
            key: 'member',
            width: 200,
            ellipsis: true,
            render: (member) => (
                <div className={styles.memberTags}>
                    {splitMembers(member).map((m) => (
                        <Tag key={m} size="small" className={styles.memberTag}>
                            {m}
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: '创建者',
            dataIndex: 'createUser',
            key: 'createUser',
            ellipsis: true
        },
        {
            title: '事业群',
            dataIndex: 'business',
            key: 'business',
            ellipsis: true
        },
        {
            title: '经理',
            dataIndex: 'manager',
            key: 'manager',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createAt',
            key: 'createAt',
            ellipsis: true,
            render: (createAt) => formatDate(createAt)
        },
        {
            title: '更新时间',
            dataIndex: 'updateAt',
            key: 'updateAt',
            ellipsis: true,
            render: (updateAt) => formatDate(updateAt)
        },
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            width: 100,
            render: (_, record) => {
                return canEdit(record) ? (
                    <Button size="small" type="primary" onClick={() => handleEdit(record)}>
                        编辑
                    </Button>
                ) : (
                    <Button size="small" onClick={() => handleApply(record)}>
                        申请
                    </Button>
                );
            }
        }
    ];

    useEffect(() => {
        getTableData();
    }, []);

    return (
        <div>
            <Button type="primary" onClick={handleCreate} style={{ margin: '16px 0' }}>
                新增群组
            </Button>

            <Card style={{ margin: ' 16px 0' }}>
                <Form form={filterForm} layout="inline" className={styles.filterForm}>
                    <Form.Item label="工作组名称1" name="name">
                        <Input placeholder="输入工作组名称" allowClear />
                    </Form.Item>
                    <Form.Item label="经理" name="manager">
                        <Input placeholder="输入经理名称" allowClear />
                    </Form.Item>
                    <Form.Item label="事业群名称" name="business">
                        <Input placeholder="请输入事业群名称" allowClear />
                    </Form.Item>
                    <Form.Item>
                        <Button type="primary" onClick={handleFilter}>
                            查询
                        </Button>
                    </Form.Item>
                </Form>
            </Card>

            <Table
                columns={columns}
                dataSource={tableData}
                loading={loading}
                pagination={false}
                rowKey="id"
                scroll={{ x: 1200 }}
            />

            <div className={styles.paginationContainer}>
                <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 条`}
                    pageSizeOptions={['10', '20', '50', '100']}
                    onChange={handleCurrentChange}
                    onShowSizeChange={handleSizeChange}
                />
            </div>

            <GroupFormDialog
                visible={dialogVisible}
                onCancel={() => setDialogVisible(false)}
                formData={currentFormData}
                isEdit={isEditMode}
                onSubmitSuccess={() => {
                    setDialogVisible(false);
                    getTableData();
                }}
            />

            <ApplyGroupDialog
                visible={applyDialogVisible}
                onCancel={() => setApplyDialogVisible(false)}
                groupId={currentGroupId}
                groupName={currentGroupName}
                admins={currentAdmins}
                onSubmitSuccess={() => {
                    setApplyDialogVisible(false);
                    getTableData();
                }}
            />
        </div>
    );
};

export default AllGroupsTable;
