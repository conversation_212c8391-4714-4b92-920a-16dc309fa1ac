.memberManageModal {
  .groupTabs {
    margin: 5px;
    padding: 10px;
    background-color: #fff;
    height: 100%;

    :global(.ant-tabs-nav) {
      margin-bottom: 0;
    }

    :global(.ant-tabs-nav-wrap::after) {
      height: 1px;
      background-color: #d9d9d9;
    }

    :global(.ant-tabs-tab) {
      padding: 0 20px;
      height: 48px;

      &:hover {
        color: #1890ff;
      }

      &.ant-tabs-tab-active {
        color: #1890ff;
      }
    }

    .tabLabel {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      .tabIcon {
        font-size: 16px;
        margin-right: 4px;
      }

      .tabText {
        font-size: 14px;
        line-height: 1;
      }
    }
  }

  .filterForm {
    margin-bottom: 16px;
  }

  .memberTags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .memberTag {
      margin: 0;
    }
  }

  .paginationContainer {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}