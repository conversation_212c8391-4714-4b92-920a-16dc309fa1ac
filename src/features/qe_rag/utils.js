//  根据名称生成头像颜色
export const getAvatarColor = (name) => {
    const colors = [
        'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // 蓝紫
        'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', // 粉红
        'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', // 蓝青
        'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', // 绿青
        'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', // 粉黄
        'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', // 青粉
        'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', // 红粉
        'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)', // 黄橙
        'linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%)', // 橙红
        'linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%)', // 蓝绿
        'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)', // 紫黄
        'linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)', // 青蓝
    ];

    // 使用名称的字符码生成一个稳定的索引
    let hash = 0;
    for (let i = 0; i < (name || '').length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    const index = Math.abs(hash) % colors.length;
    return colors[index];
};