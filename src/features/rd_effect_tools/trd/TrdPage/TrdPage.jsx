import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Tooltip, Popconfirm, Input, message, Divider } from 'antd';
import { isEmpty } from 'lodash';
import classnames from 'classnames';
import { mdSerialize, mdDeserialize } from '@baidu/morpho-data-transform';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Editor, createEditor, useStable } from '@baidu/morpho';
// utils
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import trdModel from 'COMMON/models/rd_effect_tools/trdModel';
import { getFormatTime } from 'COMMON/utils/dateUtils';
import { post } from 'COMMON/utils/requestUtils';
import { fileImageUpload, uploadContentToBos } from 'COMMON/utils/utils';
import { getDocInfoFromUrl } from 'FEATURES/front_qe_tools/case/demand/utils';
import { TESTONE_BASICURL } from 'COMMON/config/baseUrl';
// api
import { getBosToken } from 'COMMON/api/base/common';
import { updateTrd } from 'COMMON/api/rd_effect_tools/trd';
// components
import NoContent from 'COMMON/components/common/NoContent';
import styles from './TrdPage.module.less';

const POLL_INTERVAL = 3000; // 每3秒轮询一次

const TASK_STATUS = {
    ing: [0, 2, 3], // 任务状态：进行中
    done: [1, 4, 5, 6, 7], // 任务状态：已完成
    success: [5], // 任务状态：成功
    fail: [1, 4, 6, 7] // 任务状态：失败
};

function TrdPage(props) {
    const { loading, setLoading, curMrd, setCurMrd, curTask, refreshTask, reCreateTask } = props;
    const editorMd = useStable(() =>
        createEditor({
            image: {
                upload: fileImageUpload,
                base64: false
            },
            embed: false
        })
    );
    const timerRef = useRef(null); // 轮询定时器
    const [isEdit, setIsEdit] = useState(false); // 初始文档是否编辑状态
    const [title, setTitle] = useState(''); // 文档标题，初始化展示用
    const [inputValue, setInputValue] = useState(editorMd.htmlSerializer.deserialize('<div/>')); // 文档内容，初始化展示用
    const [trdDocCreatePath, setTrdDocCreatePath] = useState(
        localStorage?.getItem('trd_doc_create_path')
    );
    const [docLoading, setDocLoading] = useState(true);

    useEffect(() => {
        if (TASK_STATUS?.success?.includes(curTask?.taskStatus)) {
            getEditorView();
        }
    }, [curTask, curMrd?.kuLink]);

    // 轮询逻辑
    useEffect(() => {
        // 清除旧的定时器
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }
        // 启动轮询
        if (curTask && TASK_STATUS?.ing?.includes(curTask?.taskStatus)) {
            timerRef.current = setInterval(() => {
                refreshTask(curMrd?.docNodeId);
            }, POLL_INTERVAL);
        }

        // 清理函数：组件卸载或 curTask 变化时执行
        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
                timerRef.current = null;
            }
        };
    }, [curTask?.taskId, curMrd?.docNodeId]);

    const statusInfo = useMemo(() => {
        if (!curTask?.taskStatus) {
            return {
                code: -2,
                msg: '暂无任务'
            };
        }
        switch (curTask?.taskStatus) {
            case 0:
                return {
                    code: 0,
                    msg: '任务已创建，待执行'
                };
            case 1:
                return {
                    code: -1,
                    msg: '任务创建失败，Kirin 创建失败'
                };
            case 2:
                return {
                    code: 1,
                    msg: 'Kirin 创建成功，任务执行中...'
                };
            case 3:
                return {
                    code: 1,
                    msg: '任务执行中，预计 10 分钟可完成...'
                };
            case 4:
                return {
                    code: -1,
                    msg: '任务执行失败'
                };
            case 5:
                return {
                    code: 2,
                    msg: '任务执行成功'
                };
            case 6:
                return {
                    code: -1,
                    msg: '任务执行失败，Kirin 执行超时'
                };
            case 7:
                return {
                    code: -1,
                    msg: '任务执行失败，执行超时'
                };
            default:
                return {
                    code: 0,
                    msg: '任务获取中...'
                };
        }
    }, [curTask?.taskStatus]);

    const getEditorView = async () => {
        try {
            // 获取生成任务内容
            let trdFile = curTask?.taskResult?.trdFile;
            let _url = await getBosToken({ bosLink: trdFile });
            const jsonDetail = await fetch(trdFile + '?authorization=' + _url.token).then((r) =>
                r.json()
            );
            setTitle(jsonDetail?.title ?? '无标题文档');
            let _value = jsonDetail?.json ?? mdDeserialize(jsonDetail?.content);
            setInputValue(_value);
            setDocLoading(false);
        } catch (e) {
            console.log('error: ', e);
            setDocLoading(false);
        }
    };

    // 创建文档
    const handleCreateDoc = async () => {
        try {
            const docInfo = getDocInfoFromUrl(trdDocCreatePath);
            const repoParams = {
                spaceGuid: docInfo.spaceGuid,
                groupGuid: docInfo.groupGuid,
                repositoryGuid: docInfo.repositoryGuid,
                parentDocGuid: docInfo?.docGuid
            };
            // 更新文档内容
            localStorage.setItem('trd_doc_create_path', trdDocCreatePath);
            let res = await post(
                TESTONE_BASICURL + '/api/ku/createKuDoc?username=testOne&token=testOne',
                {
                    ...repoParams,
                    createMode: 2,
                    title: (title || '无标题文档') + getFormatTime(),
                    contentInfo: {
                        protocol: 2,
                        contentType: 1,
                        text: mdSerialize(inputValue, {
                            standard: true // 必须要有
                        })
                    }
                },
                {
                    headers: {
                        username: 'lishuang30',
                        token: '35598e041a2a09b95d9d7886dae42411'
                    }
                }
            );
            // 上传trd内容到bos
            await uploadTrdContent(title, res?.result?.url);
            message.success('文档创建成功');
            setIsEdit(false);
        } catch (e) {
            console.log(e);
        }
    };

    // 上传trd内容到bos
    const uploadTrdContent = async (_title = title, kuLink) => {
        let jsonData = {
            title: _title,
            content: mdSerialize(inputValue, {
                standard: true // 必须要有
            }),
            json: inputValue
        };
        let { url } = await uploadContentToBos(jsonData, 'json', false);
        await updateTrd({
            docNodeId: curMrd?.docNodeId,
            trdContent: url,
            kuLink: kuLink ?? ''
        });
        setCurMrd({
            ...curMrd,
            kuUpdateTime: new Date().getTime(),
            trdContent: url,
            kuLink: kuLink ?? ''
        });
    };

    // 同步文档到知识库
    const renderSyncDoc = useCallback(
        (children) => {
            return (
                <Popconfirm
                    placement="left"
                    title="确定要同步当前内容到知识库吗？"
                    description={
                        <div className={styles.popconfirmContent}>
                            <Input
                                addonBefore="指定 Ku 位置"
                                placeholder="请指定文档创建的位置"
                                value={trdDocCreatePath}
                                onChange={(e) => {
                                    setTrdDocCreatePath(e.target.value);
                                }}
                            />
                        </div>
                    }
                    okText="创建"
                    cancelText="取消"
                    onConfirm={handleCreateDoc}
                >
                    {children}
                </Popconfirm>
            );
        },
        [title, inputValue, trdDocCreatePath]
    );

    // 渲染按钮区域
    const renderButtonArea = useCallback(() => {
        let jsx = [];
        // 如果任务完成，则显示重新生成按钮
        if (TASK_STATUS?.fail?.includes(curTask?.taskStatus)) {
            return (
                <div className={styles.operatorItems}>
                    <Tooltip title="拉取最新卡片信息，重新触发生成">
                        <Button
                            size="small"
                            loading={loading}
                            type="primary"
                            className={styles.operatorItem}
                            onClick={async () => {
                                try {
                                    setLoading(true);
                                    await reCreateTask();
                                    setLoading(false);
                                } catch {
                                    setLoading(false);
                                }
                            }}
                        >
                            重新生成
                        </Button>
                    </Tooltip>
                </div>
            );
        }
        // 如果任务不成功，则不显示按钮
        if (!TASK_STATUS?.success?.includes(curTask?.taskStatus)) {
            return;
        }
        if (docLoading) {
            return (
                <div className={styles.operatorItems}>
                    <Button size="small" loading type="primary" className={styles.operatorItem}>
                        加载中...
                    </Button>
                </div>
            );
        }
        if (TASK_STATUS?.success?.includes(curTask?.taskStatus)) {
            return (
                <div className={styles.operatorItems}>
                    <Tooltip title="平台修改，暂不支持同步到知识库">
                        <Button
                            size="small"
                            type="primary"
                            onClick={async () => {
                                // 保存
                                if (isEdit) {
                                    await uploadTrdContent(title);
                                    message.success('保存成功');
                                }
                                setIsEdit(!isEdit);
                            }}
                            className={styles.operatorItem}
                        >
                            {isEdit ? '更新内容' : '编辑'}
                        </Button>
                    </Tooltip>
                    {!isEdit && (
                        <>
                            {renderSyncDoc(
                                <Button size="small" className={styles.operatorItem}>
                                    同步文档到知识库
                                </Button>
                            )}
                            <Divider type="vertical" />
                            <Tooltip title="拉取最新卡片信息，重新触发生成">
                                <Button
                                    size="small"
                                    loading={loading}
                                    type="primary"
                                    className={styles.operatorItem}
                                    onClick={async () => {
                                        try {
                                            setLoading(true);
                                            await reCreateTask();
                                            setLoading(false);
                                        } catch {
                                            setLoading(false);
                                        }
                                    }}
                                >
                                    重新生成
                                </Button>
                            </Tooltip>
                        </>
                    )}
                </div>
            );
        }
        return jsx;
    }, [docLoading, curTask, getEditorView]);

    return (
        <div className={styles.moduleContainer}>
            <div className={styles.header}>
                <div className={styles.info}>
                    <span className={styles.infoItem}>
                        <span className={styles.title}>最新同步:</span>
                        {curMrd?.kuUpdateTime ? getFormatTime(curMrd?.kuUpdateTime) : '暂无'}
                    </span>
                    <span className={styles.infoItem}>
                        <span className={styles.title}>知识库链接:</span>
                        {!isEmpty(curMrd?.kuLink) ? (
                            <a href={curMrd?.kuLink} target="_blank" rel="noopener noreferrer">
                                {curMrd?.kuLink}
                            </a>
                        ) : (
                            <span className={styles.infoDefaultContent}>
                                {isEdit && '编辑中，不可同步'}
                                {TASK_STATUS?.ing?.includes(curTask?.taskStatus) &&
                                    '任务执行中，不可同步'}
                                {TASK_STATUS?.fail?.includes(curTask?.taskStatus) &&
                                    '任务执行失败，不可同步'}
                                {!isEdit && TASK_STATUS?.success?.includes(curTask?.taskStatus) && (
                                    <>
                                        待同步文档到知识库 ,
                                        {renderSyncDoc(
                                            <a className={styles.infoDefaultContentLink}>
                                                点击同步
                                            </a>
                                        )}
                                    </>
                                )}
                            </span>
                        )}
                    </span>
                </div>
                <div className={styles.operator}>{renderButtonArea()}</div>
            </div>
            <div className={styles.webPage}>
                {curTask?.taskStatus !== 5 && (
                    <div>
                        <NoContent
                            className={styles.statusInfo}
                            text={
                                <Tooltip title={`任务 ID：${curTask?.taskId}`}>
                                    <span
                                        className={classnames({
                                            [styles.statusInfoIng]: [0, 1]?.includes(
                                                statusInfo?.code
                                            ),
                                            [styles.statusInfoSuccess]: statusInfo?.code === 2,
                                            [styles.statusInfoError]: statusInfo?.code === -1
                                        })}
                                    >
                                        {statusInfo?.msg}
                                    </span>
                                </Tooltip>
                            }
                        />
                    </div>
                )}
                {curTask?.taskStatus === 5 && (
                    <div className={styles.editor}>
                        <div className={styles.docHeader}>
                            {isEdit ? (
                                <Input
                                    placeholder="请输入标题"
                                    value={title}
                                    variant="borderless"
                                    onChange={(e) => setTitle(e.target.value)}
                                />
                            ) : (
                                <span className={styles.docTitle}>{title}</span>
                            )}
                        </div>
                        <div className={styles.morpho}>
                            <Morpho editor={editorMd}>
                                <Toolbar className={styles.editorToolbar} />
                                <Editor
                                    style={{
                                        overflow: 'scroll',
                                        marginTop: '10px',
                                        maxWidth: '100%'
                                    }}
                                    autoFocus
                                    value={inputValue}
                                    onChange={setInputValue}
                                    readOnly={!isEdit}
                                />
                            </Morpho>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default connectModel([baseModel, trdModel], (state) => ({
    kuSDK: state.common.base.kuSDK,
    token: state.common.base.token,
    curTreeNodeId: state.common.trd.curTreeNodeId
}))(TrdPage);
