import { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from 'antd';
import { <PERSON><PERSON><PERSON>, Editor, createEditor, useStable } from '@baidu/morpho';
import { getFormatTime } from 'COMMON/utils/dateUtils';
import { Loading, NoContent } from 'COMMON/components/common';
// api
import { getBosToken } from 'COMMON/api/base/common';
import styles from './MrdPage.module.less';

function MrdPage(props) {
    const { loading, setLoading, curMrd, curTask, reCreateTask } = props;
    const editorMd = useStable(() =>
        createEditor({
            image: {
                base64: false
            },
            embed: false
        })
    );
    const [inputValue, setInputValue] = useState(editorMd.htmlSerializer.deserialize('<div/>')); // 文档内容，初始化展示用

    const nodeLink = useMemo(
        () =>
            'https://console.cloud.baidu-int.com/devops/icafe/issue/' +
            curMrd?.nodeLink +
            '/show?source=copy-shortcut',
        [curMrd?.nodeLink]
    );

    useEffect(() => {
        let nodeContentLink = curMrd?.nodeContent;
        if (!nodeContentLink) {
            return;
        }
        async function func() {
            let _url = await getBosToken({ bosLink: nodeContentLink });
            const jsonDetail = await fetch(nodeContentLink + '?authorization=' + _url.token).then(
                (r) => r.json()
            );
            setInputValue(jsonDetail?.json);
        }
        func();
    }, [curMrd?.docNodeId, curMrd?.nodeContent]);

    return (
        <div className={styles.moduleContainer}>
            <div className={styles.header}>
                <div className={styles.info}>
                    <span className={styles.infoItem}>
                        <span className={styles.title}>最新生成:</span>
                        {curTask?.createTime ? getFormatTime(curTask?.createTime) : '暂无'}
                    </span>
                    <span className={styles.infoItem}>
                        <span className={styles.title}>iCafe 链接:</span>
                        <Tooltip title="点击跳转">
                            <a href={nodeLink} target="_blank" rel="noopener noreferrer">
                                {nodeLink}
                            </a>
                        </Tooltip>
                    </span>
                </div>
                <div className={styles.operator}>
                    <div className={styles.operatorItems}>
                        <Button
                            size="small"
                            loading={loading}
                            className={styles.operatorItem}
                            type="primary"
                            onClick={async () => {
                                try {
                                    setLoading(true);
                                    await reCreateTask();
                                    setLoading(false);
                                } catch {
                                    setLoading(false);
                                }
                            }}
                        >
                            同步并生成
                        </Button>
                    </div>
                </div>
            </div>
            <div className={styles.webPage}>
                {loading ? (
                    <Loading />
                ) : (
                    <div className={styles.editor}>
                        <div className={styles.morpho}>
                            {!curMrd?.nodeContent ? (
                                <NoContent className={styles.noContent} text="暂无内容" />
                            ) : (
                                <Morpho editor={editorMd}>
                                    <Editor
                                        style={{
                                            overflow: 'scroll',
                                            maxWidth: '100%'
                                        }}
                                        autoFocus
                                        value={inputValue}
                                        readOnly
                                    />
                                </Morpho>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default MrdPage;
