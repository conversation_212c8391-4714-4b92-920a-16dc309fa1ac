.noContent {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.container {
    width: 100%;
}

.viewSwitch {
    position: absolute;
    right: 4px;
    top: 45px;
    z-index: 9;
}

.setting {
    position: absolute;
    right: 130px;
    top: 15px;
    color: #777;
    z-index: 999;
    border-radius: 5px;
    padding: 2px;
    cursor: pointer;
}

.setting:hover {
    background-color: var(--base-hover-background-color);
}

.osTypeView {
    position: absolute;
    left: 15px;
    top: 52px;
    border-radius: 5px;
    z-index: 9;
    background-color: var(--background-color);
    font-size: 12px;
    color: #777;
    cursor: pointer;
}

.osTypeSwitch {
    margin-left: 5px;
    color: #777;
}

.osTypeViewWithAndroid {
    width: 30px;
}

.viewSwitchWithShow {
    right: calc(30% + 85px);
}

.confirmText {
    font-weight: bold;
}

.headerRight {
    position: absolute;
    right: 15px;
    height: 30px;
    z-index: 99;

    .title {
        display: inline-block;
        height: 40px;
        line-height: 40px;
        font-weight: bold;
    }

    .osTypeSelect {
        margin-left: 5px;

        :global {
            .ant-select-selection-item {
                font-size: 12px !important;
            }
        }
    }

    .caseRunCurProgress {
        height: 40px;
        line-height: 40px;
        width: 150px;
    }

    .signStageInfo {
        margin-left: 160px;
        font-size: 12px;
        color: var(--color2);
    }

    .caseRunStepItem {
        padding: 2px 5px;
        background-color: #eee;
        color: #777;
        font-size: 10px;
        border-radius: 5px;
        cursor: pointer;
    }

    .caseRunStepItemActive {
        box-shadow: 0 0 5px 5px rgba(122, 122, 122, 0.2);
    }

    .caseRunStepItemIng {
        background-color: #1785ff;
        color: #fff;
    }

    .caseRunStepItemSuccess {
        background-color: #48bc19;
        color: #fff;
    }

    :global {

        .ant-steps-item-active .ant-steps-item-title,
        .custom-default-steps-item-active .custom-default-steps-item-title,
        .custom-dark-steps-item-active .custom-dark-steps-item-title {
            color: var(--primary-color) !important;
            font-size: 16px;
            font-weight: bold;
        }

        .ant-space-item-split,
        .custom-default-space-item-split,
        .custom-dark-space-item-split {
            color: #b7b7b7;
            font-size: 8px;
        }
    }


    .finishText {
        font-weight: bold;
        color: var(--success-color);
    }

    .operaBtn {
        height: 30px;
        line-height: 30px;
        float: right;
    }

    .historyBtn {
        color: var(--color2);
        font-size: 13px;
        cursor: pointer;
        text-decoration: underline;
    }

    .historyBtn:hover {
        color: var(--primary-color);
    }

    .operaFinish {
        margin-right: 5px;
        color: var(--success-color);
        font-weight: bold;
    }
}

.curRound {
    :global {
        .ant-select-arrow {
            font-size: 12px !important;
        }

        .ant-select-selection-item {
            font-size: 12px !important;
            color: #777 !important;
            text-decoration: underline;
        }
    }
}

.record {
    cursor: pointer;
    display: flex;
    align-items: center;
}

@keyframes blink {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

// .historyBtnWrapper {
//     display: flex;
//     align-items: center;
// }

.recordIcon {
    width: 16px;
    /* 调整大小 */
    height: 16px;
    /* 调整大小 */
    margin-right: 4px;
    /* 调整间距 */
    // animation: blink 1s infinite; /* 动画效果 */
}

.recording {
    animation: blink 1s infinite;
    /* 动画效果 */
    opacity: 1;
    /* 完全不透明 */
}

.notRecording {
    opacity: 0.3;
    /* 半透明 */
}