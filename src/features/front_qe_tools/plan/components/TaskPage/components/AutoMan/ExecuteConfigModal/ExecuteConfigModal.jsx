import { useEffect, useState, useImper<PERSON><PERSON>andle, useCallback, forwardRef } from 'react';
import { Form, Modal, Spin, Select, Space, message, Button, Checkbox, Radio, Switch } from 'antd';
import { isEmpty } from 'lodash';
import { connectModel } from 'COMMON/middleware';
import commonModel from 'COMMON/models/commonModel';
import planModel from 'COMMON/models/planModel';
import baseModel from 'COMMON/models/baseModel';
import { getEnvList } from 'COMMON/api/front_qe_tools/config';
import {
    createCloudTask,
    getRoundDetail,
    getCloudTaskDetail,
    getCloudTaskStatus,
    getDevicePoolList
} from 'COMMON/api/front_qe_tools/plan/plan';
import {
    getNewIpRedirect,
    getNewIpRedirectReverse
} from 'COMMON/components/TreeComponents/Step/utils';
import { convertOsTypeToType } from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { CardTitle } from 'COMMON/components/common/Card';

import CloudDevice from 'COMMON/components/execution/CloudDevice';
import InstallParams from 'COMMON/components/execution/InstallParams';

import LocalDevice from 'FEATURES/front_qe_tools/plan/components/Setting/LocalDevice';
import DeviceType from 'FEATURES/front_qe_tools/plan/components/Setting/DeviceType';
import SystemPop from 'FEATURES/front_qe_tools/plan/components/Setting/SystemPop';
import LogCheck from 'FEATURES/front_qe_tools/plan/components/Setting/LogCheck';
import TaskTimeout from 'FEATURES/front_qe_tools/plan/components/Setting/TaskTimeout';
import LogCat from 'FEATURES/front_qe_tools/plan/components/Setting/LogCat';
import NotificationConfig from 'FEATURES/front_qe_tools/plan/components/Setting/NotificationConfig';
import EnvParams from 'FEATURES/front_qe_tools/plan/components/Setting/EnvParams';
import { getUrl } from 'FEATURES/front_qe_tools/regressionCase/case/utils';
import IpRedirect from 'FEATURES/front_qe_tools/plan/components/Setting/IpRedirect';
import RetryTimes from 'FEATURES/front_qe_tools/plan/components/Setting/RetryTimes';

import { filterValidCase } from 'FEATURES/front_qe_tools/plan/utils';
import CaseSelect from './NewCaseSelect';
import styles from './ExecuteConfigModal.module.less';

function ExecuteConfigModal(
    {
        currentCloudPlan,
        setCurrentCloudPlan,
        currentPlanMapGroup,
        serverList,
        currentSpace,
        curRoundId,
        curOsType
    },
    ref
) {
    const [open, setOpen] = useState(false);
    const [refStatus, setRefStatus] = useState(null);
    const [curExecuteList, setCurExecuteList] = useState([]);
    const [showMore, setShowMore] = useState(false);
    const [loading, setLoading] = useState(true);
    const [caseFilterType, setCaseFilterType] = useState([]);
    const [createLoading, setCreateLoading] = useState(false);
    const [executeDetail, setExecuteDetail] = useState({});
    const [cloudDeviceList, setCloudDeviceList] = useState([]); // 双端云设备列表
    const [envList, setEnvList] = useState([]); // 环境列表
    const [showOtherConfig, setShowOtherConfig] = useState(false); // 是否显示其他配置
    const [poolList, setPoolList] = useState([]); // 设备池列表
    const [osTypeKey, setOsTypeKey] = useState('android');
    const [createPlanOption, setCreatePlanOption] = useState({
        executeConfig: {
            android: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            ios: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            },
            server: {
                deviceType: null,
                deviceId: null,
                ipRedirect: [],
                retryTimes: 0,
                taskTimeout: 20,
                sysAlertClear: true,
                logcheckInfo: {
                    needLogCheck: false,
                    cuid: ''
                },
                envParams: {}
            }
        }
    });

    const [commonConfigForm] = Form.useForm();
    const [executeConfigForm] = Form.useForm();
    const [serverConfigForm] = Form.useForm();
    const [messageApi, contextHolder] = message.useMessage();
    useEffect(() => {
        setOsTypeKey(curOsType === 1 ? 'android' : curOsType === 2 ? 'ios' : 'server');
    }, [curOsType]);
    // 获取云设备列表、环境列表
    // 获取用例树、执行参数
    const showModal = (status) => {
        async function func() {
            setRefStatus(status);
            // 默认选择失败+异常
            if (status === 'create' && currentCloudPlan) {
                setCaseFilterType(['4', '5']);
            }
            // 默认选择待执行
            if (status === 'create' && !currentCloudPlan) {
                setCaseFilterType(['6']);
            }
            // 上一轮执行
            if (status !== 'create' && currentCloudPlan) {
                setCaseFilterType(['2']);
            }
            await obtainCloudDeviceList();
            await obtaionEnvParamsList();
            await obtainCaseTree();
            await obtaionExecuteDetail();
            setOpen(true);
        }
        func();
    };

    const hideModal = useCallback(() => {
        commonConfigForm.resetFields();
        executeConfigForm.resetFields();
        setCaseFilterType([]);
        setOpen(false);
    }, []);

    const onReset = () => {
        hideModal();
    };

    const handleCreateTask = async () => {
        try {
            await commonConfigForm?.validateFields();
            await executeConfigForm?.validateFields();
        } catch (e) {
            console.log(e);
            messageApi.warning('请填写完整表单');
            return;
        }
        const commonConfigFormValues = commonConfigForm.getFieldsValue();
        // 获取需要执行的executeList和叶子节点Id
        setCreateLoading(true);
        let params = {
            roundId: curRoundId,
            type: curOsType,
            caseNodeIdList: commonConfigFormValues.caseIdList
        };
        const executeConfigFormValues = executeConfigForm.getFieldsValue();
        let cloudParams = [];
        // 若有自动化配置
        if (executeConfigFormValues.deviceType !== 2) {
            cloudParams = {
                type: curOsType,
                retryTimes: executeConfigFormValues.retryTimes ?? 0,
                sysAlertClear: executeConfigFormValues.sysAlertClear ?? true,
                requestDirectMap: getNewIpRedirect(
                    createPlanOption?.executeConfig,
                    convertOsTypeToType(curOsType)
                ).filter((item) => {
                    return (
                        item.oriAddress.hostname !== undefined &&
                        item.targetAddress.host !== undefined
                    );
                }),
                envParams: {
                    envDetail: {
                        envId: createPlanOption?.executeConfig?.[osTypeKey]?.envParams?.envId,
                        paramList:
                            (
                                createPlanOption?.executeConfig?.[osTypeKey]?.envParams
                                    ?.paramList || []
                            )?.map((item) => ({
                                paramKey: item.paramKey,
                                envValue: item.envValue
                            })) || [],
                        appList:
                            (
                                createPlanOption?.executeConfig?.[osTypeKey]?.envParams?.appList ||
                                []
                            )?.map((item) => ({
                                appId: item.appId,
                                envValue: item.envValue
                            })) || [],
                        serverList:
                            (
                                createPlanOption?.executeConfig?.[osTypeKey]?.envParams
                                    ?.serverList || []
                            )?.map((item) => ({
                                serverId: item.serverId,
                                envValue: item.envValue
                            })) || []
                    }
                },
                // 报警
                alarmInfo: {
                    toid: executeConfigFormValues?.toid ? executeConfigFormValues?.toid : null, // 群号
                    webhook: executeConfigFormValues?.webhook, // 机器人地址
                    atuseridName: executeConfigFormValues?.atuseridName, // 需@的人
                    statusList: executeConfigFormValues?.statusList // 通知的用例状态范围；2-执行成功 3-执行不通过 4-执行异常
                }
            };
            // 仅本地设备参数 打点自动化校验
            if (executeConfigFormValues.deviceType === 3) {
                cloudParams.taskTimeout = executeConfigFormValues.taskTimeout;
                cloudParams.deviceIdList = executeConfigFormValues.localDevice;
                cloudParams.logcheckInfo = {
                    needLogCheck: executeConfigFormValues.logcheckInfo ?? false,
                    cuid: executeConfigFormValues.logcheckInfo ? executeConfigFormValues.cuid : ''
                };
            }
            // 仅云端设备参数 安装app
            if (executeConfigFormValues.deviceType === 1) {
                cloudParams.poolList = Array.isArray(executeConfigFormValues.cloudDevice)
                    ? executeConfigFormValues.cloudDevice
                    : [executeConfigFormValues.cloudDevice];
                cloudParams.installParams = executeConfigFormValues?.installParams ?? [];
                // 仅Server端参数
                if (curOsType === 4) {
                    cloudParams.modeType = executeConfigFormValues.modeType || 0;
                    // 仅选择diff模式
                    if (+executeConfigFormValues.modeType === 1) {
                        cloudParams.modeConfig = {
                            serverConfig: {
                                stableEnvironmentId: executeConfigFormValues.stableEnvironmentId,
                                testEnvironmentId: executeConfigFormValues.testEnvironmentId
                            },
                            assertConfig: {
                                // 断言配置
                                jsonSchemaCheck: executeConfigFormValues.jsonSchemaCheck, // JSON Schema 校验
                                intelligentNoiseReduce:
                                    executeConfigFormValues.intelligentNoiseReduce // 智能去噪
                            }
                        };
                    }
                }
            }
        }
        if (!isEmpty(cloudParams)) {
            params = {
                ...params,
                ...cloudParams
            };
        }
        // const serverConfigFormValues = executeConfigForm.getFieldsValue();
        // 服务端配置
        // if (!isEmpty(serverConfigFormValues)) {
        //     params = {
        //         ...params,
        //         serverConfig: serverConfigFormValues
        //     };
        // }
        // 创建
        createCloudTask(params)
            .then((res) => {
                let cloudPlanId = res?.cloudPlanId;
                getCloudTaskStatus({
                    roundId: curRoundId,
                    cloudPlanId: cloudPlanId
                }).then((task) => {
                    setCurrentCloudPlan(task);
                    messageApi.success('创建任务成功');
                });
                setCreateLoading(false);
                hideModal();
            })
            .catch((err) => {
                setCreateLoading(false);
            });
    };

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    // 获取执行配置详情
    const obtaionExecuteDetail = async () => {
        if (!currentCloudPlan?.cloudPlanId) {
            return;
        }
        let res = await getCloudTaskDetail({
            roundId: curRoundId,
            cloudPlanId: currentCloudPlan?.cloudPlanId
        });
        setExecuteDetail(res);
    };
    useEffect(() => {
        const fun = async () => {
            if (!currentSpace?.id) {
                return;
            }
            const { poolList } = await getDevicePoolList({
                moduleId: currentSpace?.id,
                poolType: 3 // 1-Android 2-iOS 3-Server
            });
            setPoolList(poolList);
        };
        fun();
    }, [currentSpace?.id]);
    useEffect(() => {
        // 初始化选择的执行用例
        let ipRedirect = [];
        (executeDetail?.requestDirectMap || [])?.forEach((element) => {
            element.isNew = false;
            element.oriAddress.url = getUrl(element.oriAddress);
            element.targetAddress.url = getUrl(element.targetAddress);
            ipRedirect.push(element);
        });
        if (
            !isEmpty(executeDetail?.deviceIdList) &&
            (executeDetail?.poolList?.[0] === 0 || executeDetail?.poolList === null)
        ) {
            // web端 非本地用例组新建 -> 自动聚焦为云端设备
            if (currentPlanMapGroup?.groupType !== 4 && !isElectron() && refStatus === 'create') {
                executeConfigForm.setFieldValue('deviceType', 1);
                executeConfigForm.setFieldValue('retryTimes', executeDetail?.retryTimes);
                executeConfigForm.setFieldValue('sysAlertClear', executeDetail?.sysAlertClear);
                // 初始化是否需要日志收集
                executeConfigForm.setFieldValue('logCollect', executeDetail?.logCollect?.needLog);
                executeConfigForm.setFieldValue('filter', executeDetail?.logCollect?.filter);
                // 初始化运行环境
                executeConfigForm.setFieldValue('envParams', executeDetail?.envParams?.envId);
                return;
            }
            console.log('本地设备');
            executeConfigForm.setFieldValue('localDevice', executeDetail?.deviceIdList);
            executeConfigForm.setFieldValue('deviceType', 3);
            executeConfigForm.setFieldValue('retryTimes', executeDetail?.retryTimes);
            executeConfigForm.setFieldValue('taskTimeout', executeDetail?.taskTimeout);
            executeConfigForm.setFieldValue('sysAlertClear', executeDetail?.sysAlertClear);
            // 初始化是否需要打点自动化校验
            executeConfigForm.setFieldValue(
                'logcheckInfo',
                executeDetail?.logcheckInfo?.needLogCheck
            );
            executeConfigForm.setFieldValue('cuid', executeDetail?.logcheckInfo?.cuid);
            // 初始化是否需要日志收集
            executeConfigForm.setFieldValue('logCollect', executeDetail?.logCollect?.needLog);
            executeConfigForm.setFieldValue('filter', executeDetail?.logCollect?.filter);
            // 初始化运行环境
            executeConfigForm.setFieldValue('envParams', executeDetail?.envParams?.envId);
        } else if (!isEmpty(executeDetail?.poolList)) {
            console.log('云端设备');
            executeConfigForm.setFieldValue('cloudDevice', executeDetail?.poolList || []);
            executeConfigForm.setFieldValue('deviceType', 1);
            executeConfigForm.setFieldValue('retryTimes', executeDetail?.retryTimes);
            executeConfigForm.setFieldValue('modeType', executeDetail?.modeType || 0);
            executeConfigForm.setFieldValue(
                'stableEnvironmentId',
                executeDetail?.modeConfig?.serverConfig?.stableEnvironmentId
            );
            executeConfigForm.setFieldValue(
                'testEnvironmentId',
                executeDetail?.modeConfig?.serverConfig?.testEnvironmentId
            );
            executeConfigForm.setFieldValue(
                'jsonSchemaCheck',
                executeDetail?.modeConfig?.assertConfig?.jsonSchemaCheck
            );
            executeConfigForm.setFieldValue(
                'intelligentNoiseReduce',
                executeDetail?.modeConfig?.assertConfig?.intelligentNoiseReduce
            );

            executeConfigForm.setFieldValue('sysAlertClear', executeDetail?.sysAlertClear);
            // 初始化是否需要日志收集
            executeConfigForm.setFieldValue('logCollect', executeDetail?.logCollect?.needLog);
            executeConfigForm.setFieldValue('filter', executeDetail?.logCollect?.filter);
            // 初始化运行环境
            executeConfigForm.setFieldValue('envParams', executeDetail?.envParams?.envId);
            // 初始化安装包列表
            executeConfigForm.setFieldValue('installParams', executeDetail?.installParams);
        } else {
            executeConfigForm.setFieldValue('deviceType', 2);
        }
        // 获取选择的keys
        let newCreatePlanOption = {
            executeConfig: {
                [osTypeKey]: {
                    ...executeDetail,
                    // ipRedirect,
                    ipRedirect: getNewIpRedirectReverse(executeDetail?.requestDirectMap)
                }
            }
        };
        setCreatePlanOption(newCreatePlanOption);
        setLoading(false);
    }, [executeDetail, curExecuteList]);

    // 获取云端设备池列表
    const obtainCloudDeviceList = async () => {
        if (!curOsType || !currentSpace?.id) {
            return;
        }
        let res = await getDevicePoolList({
            moduleId: currentSpace?.id,
            poolType: curOsType
        });
        setCloudDeviceList(res?.poolList || []);
    };

    // 获取运行环境列表
    const obtaionEnvParamsList = async () => {
        if (!curOsType) {
            return;
        }
        console.log('获取运行环境列表');
        let res = await getEnvList({
            moduleId: currentSpace?.id,
            osType: curOsType
        });
        setEnvList(res.envList);
    };

    // 获取执行用例
    const obtainCaseTree = async () => {
        try {
            if (!curRoundId) {
                return;
            }
            setLoading(true);
            let res = await getRoundDetail({ roundId: curRoundId });
            let { snippetTree, caseTree } = res;
            caseTree.nodeId = caseTree.caseNodeId;
            let tree = filterValidCase(caseTree === null ? [] : [caseTree], osTypeKey);
            setCurExecuteList(tree);
            setLoading(false);
        } catch (err) {
            setLoading(false);
        }
    };

    const getTreeData = (treeData, nodeIndexList = []) => {
        return treeData.map((element, index) => {
            nodeIndexList.push(index);
            let node = {
                ...element,
                key: nodeIndexList.join('-'),
                nodeId: element.nodeId,
                nodeName: element.nodeName,
                nodeType: element.nodeType,
                title: element.nodeName,
                children: getTreeData(element.children, nodeIndexList)
            };
            nodeIndexList.pop();
            return node;
        });
    };

    const RenderTree = useCallback(
        ({ getTreeData }) => {
            return (
                <Form.Item
                    name="caseIdList"
                    label="执行用例"
                    rules={[
                        {
                            required: true,
                            message: '请选择用例'
                        }
                    ]}
                    initialValue={[]}
                >
                    <CaseSelect
                        form={commonConfigForm}
                        caseFilterType={caseFilterType}
                        curOsType={curOsType}
                        currentCloudPlan={currentCloudPlan}
                        disabled={refStatus !== 'create'}
                        className={styles.planAddTree}
                        dataSource={getTreeData(curExecuteList)}
                    />
                </Form.Item>
            );
        },
        [curExecuteList, caseFilterType]
    );
    // console.log('currentPlanMapGroup', currentPlanMapGroup);
    return (
        <Modal
            title={refStatus === 'create' ? '自动化任务创建' : '配置详情'}
            open={open}
            transitionName=""
            destroyOnClose
            onCancel={hideModal}
            footer={
                refStatus === 'create' ? (
                    <div>
                        <Space>
                            <Button onClick={onReset}>取消</Button>
                            <Button
                                type="primary"
                                createLoading={createLoading}
                                onClick={handleCreateTask}
                            >
                                确定
                            </Button>
                        </Space>
                    </div>
                ) : null
            }
            mask="true"
            maskClosable="false"
            width={window.innerWidth * 0.6}
            centered
            style={{
                height: window.innerHeight * 0.8
            }}
        >
            {contextHolder}
            <Spin tip="正在加载中..." spinning={createLoading}>
                <div
                    className={styles.content}
                    style={{
                        height: window.innerHeight * 0.7
                    }}
                >
                    <CardTitle text="自动化执行用例" />
                    <Form
                        form={commonConfigForm}
                        requiredMark={false}
                        colon={false}
                        layout="vertical"
                        disabled={refStatus !== 'create'}
                    >
                        {refStatus === 'create' && (
                            <Form.Item
                                label="快速勾选"
                                name="caseFilterType"
                                initialValue={caseFilterType}
                                layout="horizontal"
                            >
                                <Checkbox.Group
                                    onChange={(value) => {
                                        setCaseFilterType(value);
                                    }}
                                >
                                    <Checkbox value="1">全选</Checkbox>
                                    {currentCloudPlan && (
                                        <Checkbox value="2">上一轮执行用例</Checkbox>
                                    )}
                                    <Checkbox value="3">成功用例</Checkbox>
                                    <Checkbox value="4">失败用例</Checkbox>
                                    <Checkbox value="5">异常用例</Checkbox>
                                    <Checkbox value="6">待测用例</Checkbox>
                                </Checkbox.Group>
                            </Form.Item>
                        )}
                        <RenderTree getTreeData={getTreeData} />
                    </Form>
                    <CardTitle text="自动化小助手配置" />
                    <div className={styles.cardLayout}>
                        <div className={styles.cardLayout}>
                            <Form
                                form={executeConfigForm}
                                layout="vertical"
                                requiredMark={false}
                                colon={false}
                                disabled={refStatus !== 'create'}
                                initialValues={{
                                    statusList: [3, 4]
                                }}
                            >
                                {/* <CardTitle text="设备配置" style={{ marginTop: 0 }} /> */}
                                {/* 是否使用设备选择 */}
                                <DeviceType
                                    cloudDeviceList={cloudDeviceList}
                                    form={executeConfigForm}
                                    // 组件改成了从query里面获取 这里就不用了
                                    // planType={currentPlanMapGroup?.groupType === 4 ? 2 : 1}
                                    refStatus={refStatus}
                                    disabled={refStatus !== 'create'}
                                    editType={'automan'}
                                    osType={curOsType}
                                />
                                <Form.Item
                                    noStyle
                                    shouldUpdate={(prevValues, currentValues) =>
                                        prevValues.modeType !== currentValues.modeType ||
                                        prevValues.deviceType !== currentValues.deviceType
                                    }
                                >
                                    {({ getFieldValue }) => {
                                        const modeType = getFieldValue('modeType');
                                        return (
                                            <>
                                                {getFieldValue('deviceType') !== 2 && (
                                                    <>
                                                        {/* 具体设备选择 */}
                                                        {getFieldValue('deviceType') === 1 ? (
                                                            curOsType === 4 ? (
                                                                <Form.Item
                                                                    label="执行器集群"
                                                                    name="cloudDevice"
                                                                >
                                                                    <Select
                                                                        placeholder="请选择执行器集群"
                                                                        onClick={async () => {}}
                                                                        options={poolList.map(
                                                                            (item) => ({
                                                                                value: item.poolId,
                                                                                label: item.poolName // 显示服务器名称和地址
                                                                            })
                                                                        )}
                                                                    />
                                                                </Form.Item>
                                                            ) : (
                                                                <CloudDevice
                                                                    form={executeConfigForm}
                                                                    cloudDeviceList={
                                                                        cloudDeviceList
                                                                    }
                                                                    osType={curOsType}
                                                                    disabled={
                                                                        refStatus !== 'create'
                                                                    }
                                                                />
                                                            )
                                                        ) : (
                                                            <LocalDevice
                                                                form={executeConfigForm}
                                                                osType={curOsType}
                                                                disabled={refStatus !== 'create'}
                                                            />
                                                        )}

                                                        {/* 执行重试次数 */}
                                                        {curOsType !== 4 && (
                                                            <RetryTimes form={executeConfigForm} />
                                                        )}
                                                        {curOsType === 4 && (
                                                            <>
                                                                <CardTitle
                                                                    text="执行模式"
                                                                    style={{ marginTop: 0 }}
                                                                />

                                                                <Form.Item
                                                                    // label="执行模式"
                                                                    name="modeType"
                                                                    rules={[
                                                                        {
                                                                            required: true,
                                                                            message:
                                                                                '请选择执行模式'
                                                                        }
                                                                    ]}
                                                                    initialValue={0}
                                                                >
                                                                    <Radio.Group>
                                                                        <Radio value={0}>
                                                                            普通模式
                                                                        </Radio>
                                                                        <Radio value={1}>
                                                                            Diff 模式
                                                                        </Radio>
                                                                    </Radio.Group>
                                                                </Form.Item>
                                                            </>
                                                        )}

                                                        {/* 是否安装包 */}
                                                        {getFieldValue('deviceType') === 1 &&
                                                            curOsType !== 4 && (
                                                                <InstallParams
                                                                    form={executeConfigForm}
                                                                    disabled={
                                                                        refStatus !== 'create'
                                                                    }
                                                                />
                                                            )}

                                                        {curOsType !== 4 && (
                                                            <Form.Item
                                                                label="其他配置"
                                                                name="otherConfig"
                                                                initialValue={0}
                                                                layout="horizontal"
                                                            >
                                                                <a
                                                                    style={{
                                                                        textDecoration: 'underline'
                                                                    }}
                                                                    onClick={() => {
                                                                        executeConfigForm.setFieldValue(
                                                                            'otherConfig',
                                                                            !showOtherConfig
                                                                        );
                                                                        setShowOtherConfig(
                                                                            !showOtherConfig
                                                                        );
                                                                    }}
                                                                >
                                                                    {showOtherConfig
                                                                        ? '收起'
                                                                        : '展开'}
                                                                </a>
                                                            </Form.Item>
                                                        )}
                                                        <div
                                                            style={{
                                                                display: showOtherConfig
                                                                    ? 'block'
                                                                    : 'none'
                                                            }}
                                                        >
                                                            {/* 是否使用系统弹窗 */}
                                                            <SystemPop form={executeConfigForm} />
                                                            {/* 是否打点上报 */}
                                                            {getFieldValue('deviceType') === 3 && (
                                                                <LogCheck />
                                                            )}
                                                            {/* 是否日志收集 */}
                                                            <LogCat />
                                                            {/* 日志转发配置 */}
                                                            <IpRedirect
                                                                createPlanOption={createPlanOption}
                                                                setCreatePlanOption={
                                                                    setCreatePlanOption
                                                                }
                                                                isExcecuteConfig={true}
                                                                osType={curOsType}
                                                                disabled={refStatus !== 'create'}
                                                            />
                                                            {/* 单任务超时时间 */}
                                                            {getFieldValue('deviceType') === 3 && (
                                                                <TaskTimeout
                                                                    form={executeConfigForm}
                                                                />
                                                            )}
                                                        </div>
                                                        {/* <CardTitle
                                                            text="提醒配置"
                                                            style={{ marginTop: 0 }}
                                                        /> */}
                                                        {curOsType !== 4 || modeType === 0 ? (
                                                            <EnvParams
                                                                disabled={refStatus !== 'create'}
                                                                form={executeConfigForm}
                                                                osType={curOsType}
                                                                envList={envList ?? []}
                                                                createPlanOption={createPlanOption}
                                                                setCreatePlanOption={
                                                                    setCreatePlanOption
                                                                }
                                                            />
                                                        ) : null}
                                                        {modeType === 1 ? (
                                                            <>
                                                                <CardTitle text="Server 配置" />
                                                                <Form.Item
                                                                    label="稳定版环境"
                                                                    name="stableEnvironmentId"
                                                                >
                                                                    <Select
                                                                        placeholder="请选择稳定版环境"
                                                                        options={
                                                                            envList?.map((item) => {
                                                                                return {
                                                                                    label: item.envName,
                                                                                    value: item.envId
                                                                                };
                                                                            }) ?? []
                                                                        }
                                                                        allowClear
                                                                    />
                                                                </Form.Item>
                                                                <Form.Item
                                                                    label="待测版环境"
                                                                    name="testEnvironmentId"
                                                                >
                                                                    <Select
                                                                        placeholder="请选择待测版环境"
                                                                        options={
                                                                            envList?.map((item) => {
                                                                                return {
                                                                                    label: item.envName,
                                                                                    value: item.envId
                                                                                };
                                                                            }) ?? []
                                                                        }
                                                                        allowClear
                                                                    />
                                                                </Form.Item>
                                                                <CardTitle text="断言配置" />
                                                                <Form.Item
                                                                    label="JSON Schema 校验"
                                                                    tooltip="开启后，将使用开启的 JSON Schema 断言对各服务的返回结果做校验"
                                                                    name="jsonSchemaCheck"
                                                                >
                                                                    <Switch />
                                                                </Form.Item>
                                                                <Form.Item
                                                                    label="智能去噪"
                                                                    name="intelligentNoiseReduce"
                                                                    tooltip="开启后，在 Diff 忽略 Key 断言失败时，会智能计算噪声 Key 并回写到断言，再次执行 Diff 忽略 Key 断言校验"
                                                                >
                                                                    <Switch />
                                                                </Form.Item>
                                                            </>
                                                        ) : null}
                                                        <NotificationConfig
                                                            showMore={showMore}
                                                            setShowMore={setShowMore}
                                                        />
                                                    </>
                                                )}
                                            </>
                                        );
                                    }}
                                </Form.Item>
                            </Form>
                        </div>
                    </div>
                </div>
            </Spin>
        </Modal>
    );
}

export default connectModel([baseModel, commonModel, planModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentCase: state.common.case.currentCase,
    curOsType: state.common.case.curOsType,
    caseConfig: state.common.case.caseConfig,
    caseSignProgress: state.common.case.caseSignProgress,
    currentPlanMapGroup: state.common.plan.currentPlanMapGroup,
    serverList: state.common.case.serverList
}))(forwardRef(ExecuteConfigModal));
