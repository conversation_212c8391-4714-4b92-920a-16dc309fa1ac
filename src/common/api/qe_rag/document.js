import { post, get } from 'COMMON/utils/requestUtils';

// 获取文档内容
export const getDocumentContent = (params) => {
    return post('/rag/api/document/query/content', params);
};

// 上传文件
export const upload = (params, options) => {
    return post('/rag/api/document/file/upload', params, {
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        ...options
    });
};

// 获取文档列表（分页）
export const getDocuments = (params) => {
    return post('/rag/api/document/paging', params);
};

// 删除文档
export const deleteDocument = (params) => {
    return post('/rag/api/document/delete', params);
};

// 切换文档状态
export const updateDocumentStatus = (params) => {
    return post('/rag/api/document/status/update', params, { method: 'put' });
};

// 编辑文档
export const updateDocument = (params) => {
    return post('/rag/api/document/update', params, { method: 'put' });
};

// 创建文档
export const createDocument = (params) => {
    return post('/rag/api/document/add', params);
};

// 获取文档详情
export const getDocumentDetail = (params) => {
    return get('/rag/api/document/detail', params);
};
