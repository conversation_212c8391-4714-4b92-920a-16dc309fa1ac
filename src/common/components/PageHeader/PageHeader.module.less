/* 页面头部 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
}

.titleSection {
    display: flex;
    align-items: center;
    gap: 24px;
}

.pageTitle {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
    line-height: 1.2;
}

.tabs {
    display: flex;
    gap: 8px;
    align-items: center;
}

.tab {
    font-size: 14px;
    font-weight: 500;
    color: #8b8a9e;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 12px;
    background: transparent;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    user-select: none;

    &.active {
        color: #ffffff;
        background: #605ce5;
        border-color: #605ce5;
        box-shadow: 0 4px 12px rgba(96, 92, 229, 0.25);
    }

    &:hover:not(.active) {
        color: #605ce5;
        background: rgba(96, 92, 229, 0.05);
    }
}

.actionBtn {
    background: #605ce5;
    border-color: #605ce5;
    border-radius: 8px;
    height: 36px;
    padding: 0 16px;
    font-weight: 500;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 8px rgba(96, 92, 229, 0.2);

    &:hover {
        background: #605ce5 !important;
        border-color: #605ce5;
    }

    &:disabled {
        background: #d9d9d9;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
        box-shadow: none;
    }

    :global(.anticon) {
        font-size: 12px;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .titleSection {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .tabs {
        flex-wrap: wrap;
    }

    .actionBtn {
        width: 100%;
        justify-content: center;
    }
}
