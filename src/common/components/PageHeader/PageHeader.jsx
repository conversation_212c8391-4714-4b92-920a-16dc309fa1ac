import { Button } from 'antd';
import styles from './PageHeader.module.less';
const PageHeader = ({
    title,
    // 标签配置数组 [{ key: 'my', label: '我的知识库' }, { key: 'all', label: '全部知识库' }]
    tabs = [],
    activeTab,
    // 标签切换回调函数
    onTabChange,
    // 操作按钮配置 { text: '新建知识库', icon: <PlusOutlined />, onClick: () => {} }
    actionButton,
    className = ''
}) => {
    return (
        <div className={`${styles.header} ${className}`}>
            <div className={styles.titleSection}>
                <h1 className={styles.pageTitle}>{title}</h1>
                {tabs.length > 0 && (
                    <div className={styles.tabs}>
                        {tabs.map((tab) => (
                            <span
                                key={tab.key}
                                className={`${styles.tab} ${activeTab === tab.key ? styles.active : ''}`}
                                onClick={() => onTabChange && onTabChange(tab.key)}
                            >
                                {tab.label}
                            </span>
                        ))}
                    </div>
                )}
            </div>
            {actionButton && (
                <Button
                    type="primary"
                    icon={actionButton.icon}
                    onClick={actionButton.onClick}
                    className={styles.actionBtn}
                    disabled={actionButton.disabled}
                >
                    {actionButton.text}
                </Button>
            )}
        </div>
    );
};

export default PageHeader;
