.layout {
    height: 100vh;
    overflow: hidden;
}

.right {
    background: #fff;
    overflow: hidden;
    border-radius: 20px;
    // padding: 10px;
}

.rightLayout {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.intergrationRightLayout {
    padding-left: 0;
}

.headerCenter {
    display: flex;
    align-items: center;
    gap: 16px;
}

.navTabs {
    :global(.ant-tabs-nav) {
        margin-bottom:-3px;
    }

    :global(.ant-tabs-tab) {
        padding: 8px 16px;

        .anticon {
            margin-right: 4px;
        }
    }

    :global(.ant-tabs-tab-active) {
        color: #1890ff;
    }

    :global(.ant-tabs-ink-bar) {
        background: #1890ff;
    }
}

.memberIcon {
    color: #777777;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
        color: #1890ff;
        background-color: #f0f8ff;
    }
}
.tabItem {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    font-size: 13px;
    gap: 7px;
    // width: 70px;
}