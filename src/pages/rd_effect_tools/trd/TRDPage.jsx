import { Outlet } from 'umi';
import { useMemo, useEffect, useState, useRef } from 'react';
import { isEmpty } from 'lodash';
import { Layout } from 'antd';
import classnames from 'classnames';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import { getQueryParams } from 'COMMON/utils/utils';
import { CustomResizableBox } from 'COMMON/components/common';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import { TabsSwitch } from 'LAYOUTS/BasicLayout/LayoutHeader/components';
import LayoutHeader from 'LAYOUTS/BasicLayout/LayoutHeader';
import LayoutSider from './LayoutSider';
import { OPTIONS } from './const';
import commonStyles from 'PAGES/common.module.less';
import styles from './TRDPage.module.less';

function TRDPage(props) {
    const { asider, setAsider } = props;
    const [loading, setLoading] = useState(true);
    const [outletWidth, setOutletWidth] = useState(0);
    const outletRef = useRef(null);
    const query = getQueryParams();
    const navigate = useNavigate();

    const showStageItems = useMemo(() => {
        return !isEmpty(query?.docNodeId);
    }, [query?.docNodeId]);

    useEffect(() => {
        if (!outletRef.current) {
            return;
        }

        const observer = new ResizeObserver((entries) => {
            for (let entry of entries) {
                if (entry.contentRect) {
                    setOutletWidth(entry.contentRect.width);
                }
            }
        });

        observer.observe(outletRef.current);

        return () => observer.disconnect();
    }, []);

    return (
        <Layout className={styles.layout}>
            <LayoutHeader
                showRunSetting={false}
                centerExtraStyles={{ left: window.innerWidth - outletWidth / 2 }}
                centerExtra={
                    showStageItems && (
                        <TabsSwitch
                            activedClassName={styles.activedTabsSwitch}
                            tabs={OPTIONS}
                            isActiveTab={(tab) => query?.stage === tab}
                            onClick={(tab) => {
                                navigate(
                                    stringifyUrl({
                                        url: '/trd/detail',
                                        query: {
                                            ...query,
                                            stage: tab
                                        }
                                    })
                                );
                            }}
                        />
                    )
                }
            />
            <Layout hasSider>
                <CustomResizableBox asider={asider} setAsider={setAsider} border={false}>
                    <div style={{ width: '100%' }}>
                        <LayoutSider loading={loading} setLoading={setLoading} />
                    </div>
                </CustomResizableBox>
                <Layout className={commonStyles.right}>
                    <div
                        ref={outletRef}
                        className={classnames(commonStyles.rightLayout, {
                            [commonStyles.intergrationRightLayout]: asider
                        })}
                    >
                        <Outlet key={query?.docNodeId + '_' + query?.stage} />
                    </div>
                </Layout>
            </Layout>
        </Layout>
    );
}

export default connectModel([baseModel], (state) => ({
    asider: state.common.base.asider,
    currentSpace: state.common.base.currentSpace
}))(TRDPage);
