import { useState, useEffect } from 'react';
import { isEmpty } from 'lodash';
import { Tooltip, Spin, Badge } from 'antd';
import classnames from 'classnames';
import { useNavigate } from 'umi';
import { stringifyUrl } from 'query-string';
import { SearchOutlined, FolderTwoTone } from '@ant-design/icons';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import trdModel from 'COMMON/models/rd_effect_tools/trdModel';
import { getTrdTreeNodeId, getTrdTree } from 'COMMON/api/rd_effect_tools/trd';
import { getQueryParams } from 'COMMON/utils/utils';
import NoContent from 'COMMON/components/common/NoContent';
import Search from 'COMMON/components/Search';
import AddDropdown from 'COMMON/components/NewAddDropdown/AddDropdown';
import DemandTree from './components/DemandTree';
import styles from './LayoutSider.module.less';

function LayoutSider(props) {
    const { mrdTree, setMrdTree, curTreeNodeId, setCurTreeNodeId, currentSpace } = props;
    const [loading, setLoading] = useState(false);
    const [showSearch, setShowSearch] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const [searchList, setSearchList] = useState([]);
    const [expandedKeys, setExpandedKeys] = useState([]);
    const [expandedParent, setExpandedParent] = useState(false);
    const navigate = useNavigate();
    const query = getQueryParams();

    const handleSearchClick = (value) => {
        setSearchValue(value);
    };

    const refreshMrdList = async (treeNodeId = curTreeNodeId) => {
        let { tree } = await getTrdTree({ treeNodeId: treeNodeId });

        setMrdTree(tree);
    };

    // 获取目录树
    useEffect(() => {
        EventBus.on('refreshMrdList', refreshMrdList);
        return () => {
            EventBus.off('refreshMrdList', refreshMrdList);
        };
    }, [currentSpace?.id, curTreeNodeId]);

    useEffect(() => {
        async function func() {
            if (!currentSpace?.id) {
                return;
            }
            let { treeNodeId } = await getTrdTreeNodeId({ moduleId: currentSpace?.id });
            if (currentSpace?.id !== +query?.moduleId) {
                navigate(
                    stringifyUrl({
                        url: '/trd/index',
                        query: {
                            icafeLink: query?.icafeLink, // 若存在则携带去创建
                            moduleId: currentSpace?.id
                        }
                    })
                );
            }
            setCurTreeNodeId(treeNodeId);
            refreshMrdList(treeNodeId);
        }
        func();
    }, [currentSpace?.id]);

    // 搜索：卡片名称 或 卡片id
    useEffect(() => {
        setSearchList(
            mrdTree?.filter(
                (item) => item.nodeName.includes(searchValue) || item.nodeLink.includes(searchValue)
            )
        );
    }, [searchValue, mrdTree]);

    return (
        <>
            <div className={styles.layoutSider}>
                <div className={styles.siderHeader}>
                    {!showSearch && (
                        <div className={styles.groupInfo}>
                            <FolderTwoTone twoToneColor="#777" />
                            <span className={styles.text}>需求列表</span>
                        </div>
                    )}
                    {showSearch && (
                        <Search
                            className={styles.searchStyle}
                            autoFocus={true}
                            value={searchValue}
                            onSearch={handleSearchClick}
                            onBlur={() => {
                                setShowSearch(false);
                            }}
                        />
                    )}
                    <div className={styles.iconGroup}>
                        {!showSearch && (
                            <Tooltip title="搜索">
                                <div
                                    className={classnames(styles.iconWrapper)}
                                    onClick={() => {
                                        setShowSearch(true);
                                    }}
                                >
                                    <Badge dot={searchValue !== ''}>
                                        <SearchOutlined className={styles.addIcon} />
                                    </Badge>
                                </div>
                            </Tooltip>
                        )}
                        <span className={styles.addIcon}>
                            <AddDropdown showItems={['4']} showTrdModal={query?.isCreate} />
                        </span>
                    </div>
                </div>
                <Spin spinning={loading}>
                    {isEmpty(searchList) ? (
                        <NoContent text="暂无需求" className={styles.noContent} />
                    ) : (
                        <DemandTree
                            setLoading={setLoading}
                            directoryList={searchList}
                            expandedKeys={expandedKeys}
                            expandedParent={expandedParent}
                            setExpandedParent={setExpandedParent}
                            setExpandedKeys={setExpandedKeys}
                            searchValue={searchValue}
                        />
                    )}
                </Spin>
            </div>
        </>
    );
}

export default connectModel([baseModel, trdModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    mrdTree: state.common.trd.mrdTree,
    curTreeNodeId: state.common.trd.curTreeNodeId
}))(LayoutSider);
