import { useEffect, useRef } from 'react';
import { Select, Switch } from 'antd';
import { CaretDownOutlined, SettingOutlined } from '@ant-design/icons';
import { connectModel } from 'COMMON/middleware';
import { getQueryParams } from 'COMMON/utils/utils';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import {
    getEnvDetail,
    getEnvList,
    getParamList,
    getSnippetList,
    getSchemeList,
    getAppList,
    getServerList,
    getDBList,
    getRedisList,
    getUserConfig,
    updateUserConfig
} from 'COMMON/api/front_qe_tools/config';
import { getRequestDataList } from 'COMMON/api/front_qe_tools/config/request-data';
import RunCaseSettingModal from 'FEATURES/front_qe_tools/case/edit/EditPage/Modal/RunCaseSettingModal/RunCaseSettingModal';
import styles from './index.module.less';

function RunEnv(props) {
    const {
        currentSpace,
        currentEnv,
        setCurrentEnv,
        curOsType,
        envList,
        setEnvList,
        setParamList,
        snippetList,
        setSnippetList,
        schemeList,
        setSchemeList,
        appList,
        setAppList,
        setServerList,
        setRequestDataList,
        setDbList,
        setRedisList,
        personalServices,
        setPersonalServices,
        username
    } = props;
    const runEnvRunCaseSettingRef = useRef(null);
    const query = getQueryParams();

    useEffect(() => {
        getUserConfig({
            moduleId: currentSpace?.id,
            treeNodeId: query?.treeNodeId,
            username: username
        }).then((res) => {
            setPersonalServices(res);
        });
    }, [currentSpace?.id, query?.treeNodeId, username]);

    useEffect(() => {
        const moduleId = currentSpace?.id;
        if (!moduleId) {
            return;
        }
        const fetchAll = async () => {
            try {
                const [paramRes, reqDataRes, serverRes, dbRes, redisRes] = await Promise.all([
                    getParamList({ moduleId }),
                    getRequestDataList({ moduleId }),
                    getServerList({ moduleId }),
                    getDBList({ moduleId }),
                    getRedisList({ moduleId })
                ]);

                setParamList(paramRes?.paramList || []);
                setRequestDataList(reqDataRes?.tree || []);
                setServerList(serverRes?.serverList || []);
                setDbList(dbRes || []);
                setRedisList(redisRes || []);
            } catch (error) {
                console.error('[Module Fetch Error]', error);
                // 可选：全局提示或兜底状态设置
            }
        };

        fetchAll();
    }, [currentSpace?.id]);

    useEffect(() => {
        const moduleId = currentSpace?.id;
        if (!moduleId) {
            return;
        }
        const fetchAll = async () => {
            try {
                const [envRes, snippetRes, appRes, schemeRes] = await Promise.all([
                    getEnvList({ moduleId, osType: curOsType }),
                    getSnippetList({ moduleId, osType: curOsType }),
                    getAppList({ moduleId, osType: curOsType }),
                    getSchemeList({ moduleId, osType: curOsType })
                ]);
                setEnvList({ ...envList, [curOsType]: envRes?.envList });
                setSnippetList({ ...snippetList, [curOsType]: snippetRes });
                setAppList({ ...appList, [curOsType]: appRes?.appList });
                setSchemeList({ ...schemeList, [curOsType]: schemeRes });
            } catch (error) {
                console.error('[Module Fetch Error]', error);
                // 可选：全局提示或兜底状态设置
            }
        };
        fetchAll();
    }, [currentSpace?.id, curOsType]);

    useEffect(() => {
        if (!curOsType) {
            return;
        }
        let env = envList?.[curOsType]?.find((item) => item?.envId === currentEnv?.envId);
        if (!env && envList?.[curOsType]?.length) {
            env = envList?.[curOsType]?.[0];
        }
        if (env?.envId) {
            getEnvDetail({ envId: env?.envId }).then((res) => {
                setCurrentEnv({
                    ...env,
                    ...res.envDetail
                });
            });
        } else {
            setCurrentEnv(env);
        }
    }, [envList?.[curOsType]]);
    return (
        <>
            <Select
                options={envList?.[curOsType]?.map((item) => ({
                    label: item.envName,
                    value: item.envId
                }))}
                placeholder="未设置运行环境"
                variant="borderless"
                value={currentEnv?.envId}
                popupMatchSelectWidth={false}
                onChange={(value) => {
                    let curEnv = envList?.[curOsType]?.find((item) => item.envId === value);
                    setCurrentEnv(curEnv);
                }}
                suffixIcon={<CaretDownOutlined style={{ pointerEvents: 'none' }} />}
                dropdownRender={(menu) => {
                    return (
                        <div className={styles.runSettingEnv}>
                            <div className={styles.runSettingTitle}>
                                调试环境
                                <span
                                    onClick={() => runEnvRunCaseSettingRef?.current?.show()}
                                    className={styles.runSettingBtn}
                                >
                                    <SettingOutlined />
                                </span>
                            </div>
                            {menu}
                            {curOsType === 4 && (
                                <div className={styles.personalServices}>
                                    个人服务
                                    <Switch
                                        checkedChildren="开启"
                                        unCheckedChildren="关闭"
                                        checked={personalServices?.switchButton ?? false}
                                        onChange={(checked) => {
                                            const newPersonalServices = {
                                                ...personalServices,
                                                switchButton: checked
                                            };
                                            updateUserConfig({
                                                moduleId: currentSpace?.id,
                                                treeNodeId: query?.treeNodeId,
                                                username: username,
                                                envUserConf: {
                                                    switchButton: checked,
                                                    configInfo: personalServices?.configInfo || []
                                                }
                                            });
                                            setPersonalServices(newPersonalServices);
                                        }}
                                    />
                                </div>
                            )}
                        </div>
                    );
                }}
            />
            <RunCaseSettingModal ref={runEnvRunCaseSettingRef} />
        </>
    );
}

export default connectModel([baseModel, commonModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    currentEnv: state.common.base.currentEnv,
    envList: state.common.case.envList,
    paramList: state.common.case.paramList,
    snippetList: state.common.case.snippetList,
    requestDataList: state.common.case.requestDataList,
    appList: state.common.case.appList,
    serverList: state.common.case.serverList,
    schemeList: state.common.case.schemeList,
    dbList: state.common.case.dbList,
    redisList: state.common.case.redisList,
    personalServices: state.common.case.personalServices,
    username: state.common.base.username
}))(RunEnv);
