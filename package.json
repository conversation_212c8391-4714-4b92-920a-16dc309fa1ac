{"name": "qamate", "version": "1.0.0", "main": "index.js", "repository": "https://<EMAIL>/baidu/ep-qa/qamate", "author": "lishuang30 <<EMAIL>>", "license": "MIT", "scripts": {"dev": "platform=rag umi dev", "dev:rag": "umi dev", "dev:test": "cross-env ENV_MODE=test umi dev", "dev:prod": "cross-env ENV_MODE=prod umi dev", "dev:electron": "cross-env BUILD_ENV=electron umi dev", "build": "cross-env ENV_MODE=dev umi build", "build:rag": "cross-env ENV_MODE=dev platform=rag umi build", "build:dev": "cross-env ENV_MODE=dev umi build", "build:test": "cross-env ENV_MODE=test umi build", "build:prod": "cross-env ENV_MODE=prod umi build", "build:electron": "cross-env BUILD_ENV=electron umi build"}, "dependencies": {"@ant-design/icons": "5.3.1", "@baidu/ep-to-bos": "^0.1.8", "@baidu/ku-mega-comp-button": "^2.0.11", "@baidu/ku-mega-comp-toast": "^2.0.11", "@baidu/ku-mega-sdk": "^2.0.1", "@baidu/ku-mega-sdk-2.0.1": "npm:@baidu/ku-mega-sdk@2.0.1", "@baidu/morpho": "2.7.317", "@baidu/morpho-data-transform": "^0.0.46", "@codemirror/lang-html": "^6.4.6", "@monaco-editor/react": "^4.6.0", "@umijs/plugins": "^4.4.11", "antd": "5.24.5", "bootstrap": "^5.3.1", "classnames": "^2.3.2", "codemirror": "^6.0.1", "cron-validator": "^1.4.0", "dayjs": "^1.11.12", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "eslint": "^8.46.0", "hotbox-ui": "^1.0.0", "hotboxkit": "^1.0.2", "jquery": "^3.7.1", "json-to-ast": "^2.1.0", "jsonc-parser": "^3.3.1", "jsoneditor": "^9.10.2", "jsoneditor-react": "^3.1.2", "kity": "^2.0.4", "kityminder-core": "^1.4.50", "marked": "^8.0.1", "monaco-editor": "0.43.0", "qrcode.react": "^3.1.0", "quagga": "^0.12.1", "react-color": "^2.19.3", "react-json-view": "^1.21.3", "react-redux": "^8.1.2", "react-resizable": "^3.0.5", "react-webcam": "^7.2.0", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "seajs": "^3.0.3", "umi": "4.3.29", "uuid": "^10.0.0", "viewerjs": "^1.11.5", "websocket": "^1.0.35"}, "devDependencies": {"cross-env": "^7.0.3", "jscpd": "^4.0.5", "monaco-editor-webpack-plugin": "^7.1.0"}}