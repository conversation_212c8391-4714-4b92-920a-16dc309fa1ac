#!/bin/bash

# QAMate 自定义域名部署脚本
# 使用方法: ./scripts/deploy-custom-domain.sh [环境] [域名]
# 示例: ./scripts/deploy-custom-domain.sh prod https://your-domain.com

set -e

# 参数检查
if [ $# -lt 2 ]; then
    echo "使用方法: $0 [环境] [域名]"
    echo "环境选项: dev, test, prod"
    echo "示例: $0 prod https://your-domain.com"
    exit 1
fi

ENVIRONMENT=$1
DOMAIN=$2
PROJECT="qamate"
OUTPUT="dist"

echo "🚀 开始部署 QAMate 到自定义域名"
echo "环境: $ENVIRONMENT"
echo "域名: $DOMAIN"
echo "项目: $PROJECT"

# 设置环境变量
case $ENVIRONMENT in
    "prod")
        export ENV_MODE=prod
        export PROD_DOMAIN=$DOMAIN
        BUILD_COMMAND="build:prod"
        ;;
    "test")
        export ENV_MODE=test
        export TEST_DOMAIN=$DOMAIN
        BUILD_COMMAND="build:test"
        ;;
    "dev")
        export ENV_MODE=dev
        BUILD_COMMAND="build:dev"
        ;;
    *)
        echo "❌ 不支持的环境: $ENVIRONMENT"
        echo "支持的环境: dev, test, prod"
        exit 1
        ;;
esac

echo "📦 安装依赖..."
echo "Node 版本: $(node -v)"
echo "Yarn 版本: $(yarn -v)"
yarn install

echo "🔨 构建项目..."
npm run $BUILD_COMMAND

echo "📁 构建完成，输出目录: $OUTPUT"
echo "📊 构建统计:"
du -sh $OUTPUT

echo "✅ 部署准备完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 将 $OUTPUT 目录内容上传到您的服务器"
echo "2. 配置 Nginx 或其他 Web 服务器"
echo "3. 确保域名 DNS 解析正确"
echo "4. 配置 SSL 证书（推荐）"
echo ""
echo "🌐 访问地址: $DOMAIN"
