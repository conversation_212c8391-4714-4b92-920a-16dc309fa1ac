# QAMate 项目线上部署域名配置指南

## 🌐 当前域名配置

### 环境对应域名
- **开发环境**: `rmtc-offline.bcc-bdbl.baidu.com`
- **测试环境**: `pioneer.baidu-int.com`
- **生产环境**: `qamate.baidu-int.com`
- **CDN域名**: `fe-cdn.cdn.bcebos.com`

## 🚀 自定义域名配置

### 1. 环境变量配置

在部署时设置以下环境变量：

```bash
# 生产环境域名
export PROD_DOMAIN=https://your-production-domain.com

# 测试环境域名
export TEST_DOMAIN=https://your-test-domain.com

# CDN域名
export CDN_DOMAIN=https://your-cdn-domain.com

# 环境模式
export ENV_MODE=prod
```

### 2. 构建命令

```bash
# 生产环境构建
npm run build:prod

# 测试环境构建
npm run build:test

# 开发环境构建
npm run build:dev
```

### 3. 部署流程

#### 方式一：使用 CI/CD 自动部署
项目已配置 CI/CD 流程，支持以下分支：
- `dev` - 开发环境自动部署
- `test` - 测试环境自动部署
- `master` - 生产环境自动部署
- `httpsmaster` - HTTPS 生产环境部署

#### 方式二：手动部署
```bash
# 1. 设置环境变量
export ENV_MODE=prod
export PROD_DOMAIN=https://your-domain.com

# 2. 构建项目
npm run build:prod

# 3. 部署到服务器
# 将 dist 目录内容上传到您的服务器
```

## 🔧 Nginx 配置示例

```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name your-domain.com;
    
    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 静态文件目录
    root /path/to/your/dist;
    index index.html;
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api/ {
        proxy_pass https://your-api-domain.com;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 📋 域名配置检查清单

- [ ] 域名 DNS 解析配置
- [ ] SSL 证书配置
- [ ] CDN 配置（如需要）
- [ ] API 服务器配置
- [ ] 环境变量设置
- [ ] 构建和部署脚本更新
- [ ] 防火墙和安全组配置

## 🔍 常见问题

### Q: 如何更改生产环境域名？
A: 设置 `PROD_DOMAIN` 环境变量，然后重新构建部署。

### Q: 静态资源加载失败怎么办？
A: 检查 `CDN_DOMAIN` 配置是否正确，确保 CDN 服务正常。

### Q: API 请求跨域问题？
A: 确保后端 API 服务器配置了正确的 CORS 策略。
